'use client';

import { motion } from 'framer-motion';
import { 
  Hammer, 
  Palette, 
  Building, 
  Wrench, 
  Shield, 
  Truck,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const Services = () => {
  const services = [
    {
      icon: Palette,
      title: "Custom Design",
      description: "Bespoke architectural elements tailored to your vision and project requirements.",
      features: ["3D Modeling", "CAD Drawings", "Material Selection", "Design Consultation"],
      color: "from-purple-500 to-purple-700"
    },
    {
      icon: Hammer,
      title: "Expert Craftsmanship",
      description: "Master artisans with decades of experience in traditional and modern techniques.",
      features: ["Hand Carving", "Cast Stone Production", "Surface Finishing", "Quality Control"],
      color: "from-blue-500 to-blue-700"
    },
    {
      icon: Building,
      title: "Installation Services",
      description: "Professional installation ensuring perfect fit and long-lasting durability.",
      features: ["Site Assessment", "Professional Installation", "Structural Integration", "Final Inspection"],
      color: "from-green-500 to-green-700"
    },
    {
      icon: Wrench,
      title: "Restoration & Repair",
      description: "Specialized restoration services for historic and heritage buildings.",
      features: ["Damage Assessment", "Period-Accurate Restoration", "Structural Repairs", "Preservation Techniques"],
      color: "from-orange-500 to-orange-700"
    },
    {
      icon: Shield,
      title: "Quality Assurance",
      description: "Rigorous testing and quality control to ensure exceptional standards.",
      features: ["Material Testing", "Weather Resistance", "Structural Integrity", "Lifetime Warranty"],
      color: "from-red-500 to-red-700"
    },
    {
      icon: Truck,
      title: "Logistics & Delivery",
      description: "Secure packaging and timely delivery to project sites worldwide.",
      features: ["Custom Packaging", "Global Shipping", "Installation Support", "Project Coordination"],
      color: "from-indigo-500 to-indigo-700"
    }
  ];

  const process = [
    {
      step: "01",
      title: "Consultation",
      description: "Initial meeting to understand your vision, requirements, and project scope."
    },
    {
      step: "02",
      title: "Design & Planning",
      description: "Detailed design development with 3D modeling and technical specifications."
    },
    {
      step: "03",
      title: "Production",
      description: "Expert craftsmanship using premium materials and time-tested techniques."
    },
    {
      step: "04",
      title: "Installation",
      description: "Professional installation with attention to detail and structural integrity."
    }
  ];

  return (
    <section id="services" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-block mb-4">
            <span className="text-purple-600 font-semibold text-lg tracking-wide uppercase">
              Our Services
            </span>
            <div className="w-20 h-1 bg-purple-600 mt-2 mx-auto"></div>
          </div>
          
          <h2 className="font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6">
            Comprehensive
            <span className="text-gradient block">Solutions</span>
          </h2>
          
          <p className="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
            From initial concept to final installation, we provide end-to-end services 
            for all your architectural stone needs.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className="group"
            >
              <div className="bg-white rounded-xl p-8 luxury-shadow hover:shadow-2xl transition-all duration-500 border border-stone-100 h-full">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-r ${service.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="font-semibold text-xl text-stone-800 mb-3 group-hover:text-purple-600 transition-colors duration-300">
                  {service.title}
                </h3>
                
                <p className="text-stone-600 leading-relaxed mb-6">
                  {service.description}
                </p>

                {/* Features */}
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: (index * 0.1) + (featureIndex * 0.05) }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-2 text-stone-600"
                    >
                      <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </motion.li>
                  ))}
                </ul>

                {/* CTA */}
                <motion.button
                  whileHover={{ x: 5 }}
                  className="flex items-center space-x-2 text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300"
                >
                  <span>Learn More</span>
                  <ArrowRight className="w-4 h-4" />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Process Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-stone-50 rounded-2xl p-8 md:p-12"
        >
          <div className="text-center mb-12">
            <h3 className="font-display text-3xl md:text-4xl font-bold text-stone-800 mb-4">
              Our Process
            </h3>
            <p className="text-lg text-stone-600 max-w-2xl mx-auto">
              A streamlined approach that ensures exceptional results from concept to completion.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {process.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center relative"
              >
                {/* Step Number */}
                <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-600 text-white rounded-full font-bold text-xl mb-4">
                  {step.step}
                </div>

                {/* Connector Line */}
                {index < process.length - 1 && (
                  <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-purple-200 transform -translate-x-8" />
                )}

                <h4 className="font-semibold text-lg text-stone-800 mb-2">
                  {step.title}
                </h4>
                
                <p className="text-stone-600 text-sm leading-relaxed">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-12 text-white">
            <h3 className="font-display text-3xl md:text-4xl font-bold mb-4">
              Ready to Start Your Project?
            </h3>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Let our experts help you bring your architectural vision to life with premium stone craftsmanship.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-stone-50 transition-colors duration-300"
              >
                Get Free Consultation
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-300"
              >
                View Portfolio
              </motion.button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Services;
