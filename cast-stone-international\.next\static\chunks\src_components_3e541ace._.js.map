{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X, ChevronDown } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { \n      name: 'Products', \n      href: '#products',\n      dropdown: [\n        { name: 'Natural Stone', href: '#natural-stone' },\n        { name: 'Cast Stone', href: '#cast-stone' },\n        { name: 'Architectural Elements', href: '#architectural' },\n        { name: 'Custom Designs', href: '#custom' }\n      ]\n    },\n    { name: 'Portfolio', href: '#portfolio' },\n    { name: 'Services', href: '#services' },\n    { name: 'About', href: '#about' },\n    { name: 'Contact', href: '#contact' }\n  ];\n\n  const handleDropdownToggle = (itemName: string) => {\n    setActiveDropdown(activeDropdown === itemName ? null : itemName);\n  };\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex-shrink-0\"\n          >\n            <a href=\"#home\" className=\"flex items-center space-x-2\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">CS</span>\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"font-display text-xl font-bold text-stone-800\">\n                  Cast Stone International\n                </h1>\n                <p className=\"text-xs text-stone-600\">Architectural Excellence</p>\n              </div>\n            </a>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <div key={item.name} className=\"relative group\">\n                {item.dropdown ? (\n                  <div>\n                    <button\n                      onClick={() => handleDropdownToggle(item.name)}\n                      className=\"flex items-center space-x-1 text-stone-700 hover:text-purple-600 transition-colors duration-200 font-medium\"\n                    >\n                      <span>{item.name}</span>\n                      <ChevronDown className=\"w-4 h-4\" />\n                    </button>\n                    \n                    <AnimatePresence>\n                      {activeDropdown === item.name && (\n                        <motion.div\n                          initial={{ opacity: 0, y: 10 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          exit={{ opacity: 0, y: 10 }}\n                          transition={{ duration: 0.2 }}\n                          className=\"absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-stone-200 py-2\"\n                        >\n                          {item.dropdown.map((dropdownItem) => (\n                            <a\n                              key={dropdownItem.name}\n                              href={dropdownItem.href}\n                              className=\"block px-4 py-2 text-stone-700 hover:bg-purple-50 hover:text-purple-600 transition-colors duration-200\"\n                              onClick={() => setActiveDropdown(null)}\n                            >\n                              {dropdownItem.name}\n                            </a>\n                          ))}\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n                ) : (\n                  <motion.a\n                    href={item.href}\n                    whileHover={{ y: -2 }}\n                    className=\"text-stone-700 hover:text-purple-600 transition-colors duration-200 font-medium relative\"\n                  >\n                    {item.name}\n                    <motion.div\n                      className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-purple-600\"\n                      whileHover={{ width: '100%' }}\n                      transition={{ duration: 0.3 }}\n                    />\n                  </motion.a>\n                )}\n              </div>\n            ))}\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary\"\n            >\n              Get Quote\n            </motion.button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <motion.button\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-stone-700 hover:text-purple-600 transition-colors duration-200\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"lg:hidden bg-white border-t border-stone-200\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navItems.map((item) => (\n                <div key={item.name}>\n                  {item.dropdown ? (\n                    <div>\n                      <button\n                        onClick={() => handleDropdownToggle(item.name)}\n                        className=\"flex items-center justify-between w-full text-left text-stone-700 hover:text-purple-600 transition-colors duration-200 font-medium py-2\"\n                      >\n                        <span>{item.name}</span>\n                        <ChevronDown className={`w-4 h-4 transition-transform duration-200 ${\n                          activeDropdown === item.name ? 'rotate-180' : ''\n                        }`} />\n                      </button>\n                      \n                      <AnimatePresence>\n                        {activeDropdown === item.name && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: 'auto' }}\n                            exit={{ opacity: 0, height: 0 }}\n                            transition={{ duration: 0.2 }}\n                            className=\"ml-4 mt-2 space-y-2\"\n                          >\n                            {item.dropdown.map((dropdownItem) => (\n                              <a\n                                key={dropdownItem.name}\n                                href={dropdownItem.href}\n                                className=\"block text-stone-600 hover:text-purple-600 transition-colors duration-200 py-1\"\n                                onClick={() => {\n                                  setIsOpen(false);\n                                  setActiveDropdown(null);\n                                }}\n                              >\n                                {dropdownItem.name}\n                              </a>\n                            ))}\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </div>\n                  ) : (\n                    <a\n                      href={item.href}\n                      className=\"block text-stone-700 hover:text-purple-600 transition-colors duration-200 font-medium py-2\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      {item.name}\n                    </a>\n                  )}\n                </div>\n              ))}\n              \n              <motion.button\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-primary w-full mt-4\"\n                onClick={() => setIsOpen(false)}\n              >\n                Get Quote\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBAAE,MAAM;oBAAiB,MAAM;gBAAiB;gBAChD;oBAAE,MAAM;oBAAc,MAAM;gBAAc;gBAC1C;oBAAE,MAAM;oBAA0B,MAAM;gBAAiB;gBACzD;oBAAE,MAAM;oBAAkB,MAAM;gBAAU;aAC3C;QACH;QACA;YAAE,MAAM;YAAa,MAAM;QAAa;QACxC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,mBAAmB,WAAW,OAAO;IACzD;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAW,CAAC,4DAA4D,EACtE,aACI,2CACA,kBACJ;;0BAEF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,WAAU;sCAEV,cAAA,6LAAC;gCAAE,MAAK;gCAAQ,WAAU;;kDACxB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAgD;;;;;;0DAG9D,6LAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;;sCAM5C,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC;wCAAoB,WAAU;kDAC5B,KAAK,QAAQ,iBACZ,6LAAC;;8DACC,6LAAC;oDACC,SAAS,IAAM,qBAAqB,KAAK,IAAI;oDAC7C,WAAU;;sEAEV,6LAAC;sEAAM,KAAK,IAAI;;;;;;sEAChB,6LAAC,uNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;;8DAGzB,6LAAC,4LAAA,CAAA,kBAAe;8DACb,mBAAmB,KAAK,IAAI,kBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAC5B,MAAM;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC1B,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,WAAU;kEAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC;gEAEC,MAAM,aAAa,IAAI;gEACvB,WAAU;gEACV,SAAS,IAAM,kBAAkB;0EAEhC,aAAa,IAAI;+DALb,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;iEAalC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,MAAM,KAAK,IAAI;4CACf,YAAY;gDAAE,GAAG,CAAC;4CAAE;4CACpB,WAAU;;gDAET,KAAK,IAAI;8DACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAO;oDAC5B,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;uCA5C1B,KAAK,IAAI;;;;;8CAmDrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;8CACE,KAAK,QAAQ,iBACZ,6LAAC;;0DACC,6LAAC;gDACC,SAAS,IAAM,qBAAqB,KAAK,IAAI;gDAC7C,WAAU;;kEAEV,6LAAC;kEAAM,KAAK,IAAI;;;;;;kEAChB,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAW,CAAC,0CAA0C,EACjE,mBAAmB,KAAK,IAAI,GAAG,eAAe,IAC9C;;;;;;;;;;;;0DAGJ,6LAAC,4LAAA,CAAA,kBAAe;0DACb,mBAAmB,KAAK,IAAI,kBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDACjC,SAAS;wDAAE,SAAS;wDAAG,QAAQ;oDAAO;oDACtC,MAAM;wDAAE,SAAS;wDAAG,QAAQ;oDAAE;oDAC9B,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,WAAU;8DAET,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,6BAClB,6LAAC;4DAEC,MAAM,aAAa,IAAI;4DACvB,WAAU;4DACV,SAAS;gEACP,UAAU;gEACV,kBAAkB;4DACpB;sEAEC,aAAa,IAAI;2DARb,aAAa,IAAI;;;;;;;;;;;;;;;;;;;;6DAgBlC,6LAAC;wCACC,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,UAAU;kDAExB,KAAK,IAAI;;;;;;mCA7CN,KAAK,IAAI;;;;;0CAmDrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;gCACV,SAAS,IAAM,UAAU;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA3NM;KAAA;uCA6NS", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ChevronDown, Play, ArrowRight } from 'lucide-react';\nimport { useState } from 'react';\n\nconst Hero = () => {\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\n\n  const scrollToNext = () => {\n    const nextSection = document.getElementById('about');\n    nextSection?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section id=\"home\" className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image/Video */}\n      <div className=\"absolute inset-0 z-0\">\n        {isVideoPlaying ? (\n          <video\n            autoPlay\n            muted\n            loop\n            className=\"w-full h-full object-cover\"\n            poster=\"https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\"\n          >\n            <source src=\"https://player.vimeo.com/external/434045526.sd.mp4?s=c27eecc69a27dbc4ff2b87d38afc35f1a9e7c02d&profile_id=164&oauth2_token_id=57447761\" type=\"video/mp4\" />\n          </video>\n        ) : (\n          <div \n            className=\"w-full h-full bg-cover bg-center bg-no-repeat\"\n            style={{\n              backgroundImage: `url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`\n            }}\n          />\n        )}\n        \n        {/* Overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-transparent\" />\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 z-10\">\n        <motion.div\n          animate={{ y: [0, -20, 0] }}\n          transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n          className=\"absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400 rounded-full opacity-60\"\n        />\n        <motion.div\n          animate={{ y: [0, -30, 0] }}\n          transition={{ duration: 5, repeat: Infinity, ease: \"easeInOut\", delay: 1 }}\n          className=\"absolute top-1/3 right-1/3 w-3 h-3 bg-stone-300 rounded-full opacity-40\"\n        />\n        <motion.div\n          animate={{ y: [0, -25, 0] }}\n          transition={{ duration: 6, repeat: Infinity, ease: \"easeInOut\", delay: 2 }}\n          className=\"absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full opacity-50\"\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 1, delay: 0.2 }}\n          className=\"space-y-8\"\n        >\n          {/* Main Heading */}\n          <div className=\"space-y-4\">\n            <motion.h1\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"font-display text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight\"\n            >\n              Architectural\n              <span className=\"block text-gradient\">Excellence</span>\n            </motion.h1>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"text-xl md:text-2xl text-stone-200 max-w-3xl mx-auto leading-relaxed\"\n            >\n              Crafting timeless beauty with premium cast stone and natural materials. \n              Transform your vision into architectural masterpieces.\n            </motion.p>\n          </div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary text-lg px-8 py-4 flex items-center space-x-2\"\n            >\n              <span>Explore Portfolio</span>\n              <ArrowRight className=\"w-5 h-5\" />\n            </motion.button>\n            \n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setIsVideoPlaying(!isVideoPlaying)}\n              className=\"flex items-center space-x-3 text-white hover:text-purple-300 transition-colors duration-300\"\n            >\n              <div className=\"w-16 h-16 rounded-full border-2 border-white/30 flex items-center justify-center hover:border-purple-300 transition-colors duration-300\">\n                <Play className=\"w-6 h-6 ml-1\" />\n              </div>\n              <span className=\"text-lg font-medium\">Watch Our Story</span>\n            </motion.button>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1 }}\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 pt-16 border-t border-white/20\"\n          >\n            {[\n              { number: '500+', label: 'Projects Completed' },\n              { number: '25+', label: 'Years Experience' },\n              { number: '50+', label: 'Master Craftsmen' }\n            ].map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.6, delay: 1.2 + index * 0.1 }}\n                className=\"text-center\"\n              >\n                <div className=\"font-display text-4xl md:text-5xl font-bold text-white mb-2\">\n                  {stat.number}\n                </div>\n                <div className=\"text-stone-300 text-lg\">\n                  {stat.label}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\"\n      >\n        <motion.button\n          onClick={scrollToNext}\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity, ease: \"easeInOut\" }}\n          className=\"flex flex-col items-center space-y-2 text-white/70 hover:text-white transition-colors duration-300\"\n        >\n          <span className=\"text-sm font-medium\">Scroll to explore</span>\n          <ChevronDown className=\"w-6 h-6\" />\n        </motion.button>\n      </motion.div>\n\n      {/* Decorative Elements */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-stone-50 to-transparent z-10\" />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,OAAO;;IACX,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,eAAe;QACnB,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,aAAa,eAAe;YAAE,UAAU;QAAS;IACnD;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;;oBACZ,+BACC,6LAAC;wBACC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAU;wBACV,QAAO;kCAEP,cAAA,6LAAC;4BAAO,KAAI;4BAAwI,MAAK;;;;;;;;;;6CAG3J,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,mHAAmH,CAAC;wBACxI;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAAC;wBAC1B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAY;wBAC/D,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAAC;wBAC1B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;4BAAa,OAAO;wBAAE;wBACzE,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAAC;wBAC1B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;4BAAa,OAAO;wBAAE;wBACzE,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAG,OAAO;oBAAI;oBACtC,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;wCACX;sDAEC,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;;;;;;;sCAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAGxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS,IAAM,kBAAkB,CAAC;oCAClC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;sCAK1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAE;4BACtC,WAAU;sCAET;gCACC;oCAAE,QAAQ;oCAAQ,OAAO;gCAAqB;gCAC9C;oCAAE,QAAQ;oCAAO,OAAO;gCAAmB;gCAC3C;oCAAE,QAAQ;oCAAO,OAAO;gCAAmB;6BAC5C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;;mCAVR,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAmBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAY;oBAC/D,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAsB;;;;;;sCACtC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAK3B,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;GAvKM;KAAA;uCAyKS", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/About.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { useRef } from 'react';\nimport { Award, Users, Clock, Target } from 'lucide-react';\n\nconst About = () => {\n  const sectionRef = useRef<HTMLElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: sectionRef,\n    offset: [\"start end\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [100, -100]);\n  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);\n\n  const features = [\n    {\n      icon: Award,\n      title: \"Premium Quality\",\n      description: \"Using only the finest materials and time-tested techniques to ensure lasting beauty and durability.\"\n    },\n    {\n      icon: Users,\n      title: \"Expert Craftsmen\",\n      description: \"Our team of master artisans brings decades of experience in architectural stone craftsmanship.\"\n    },\n    {\n      icon: Clock,\n      title: \"Timely Delivery\",\n      description: \"Committed to meeting project deadlines without compromising on quality or attention to detail.\"\n    },\n    {\n      icon: Target,\n      title: \"Custom Solutions\",\n      description: \"Tailored designs that perfectly match your architectural vision and project requirements.\"\n    }\n  ];\n\n  return (\n    <section id=\"about\" ref={sectionRef} className=\"py-24 bg-stone-50 relative overflow-hidden\">\n      {/* Background Elements */}\n      <motion.div\n        style={{ y }}\n        className=\"absolute inset-0 opacity-5\"\n      >\n        <div \n          className=\"w-full h-full bg-cover bg-center\"\n          style={{\n            backgroundImage: `url('https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`\n          }}\n        />\n      </motion.div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.2 }}\n                viewport={{ once: true }}\n                className=\"inline-block\"\n              >\n                <span className=\"text-purple-600 font-semibold text-lg tracking-wide uppercase\">\n                  About Us\n                </span>\n                <div className=\"w-20 h-1 bg-purple-600 mt-2\"></div>\n              </motion.div>\n              \n              <motion.h2\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"font-display text-4xl md:text-5xl font-bold text-stone-800 leading-tight\"\n              >\n                Crafting Architectural\n                <span className=\"text-gradient block\">Masterpieces</span>\n              </motion.h2>\n              \n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n                viewport={{ once: true }}\n                className=\"text-lg text-stone-600 leading-relaxed\"\n              >\n                For over two decades, Cast Stone International has been at the forefront of \n                architectural stone craftsmanship. We specialize in creating bespoke cast stone \n                elements that transform ordinary buildings into extraordinary landmarks.\n              </motion.p>\n              \n              <motion.p\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.5 }}\n                viewport={{ once: true }}\n                className=\"text-lg text-stone-600 leading-relaxed\"\n              >\n                Our commitment to excellence, combined with innovative techniques and sustainable \n                practices, has made us the preferred choice for architects, designers, and \n                developers worldwide.\n              </motion.p>\n            </div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              viewport={{ once: true }}\n              className=\"flex flex-col sm:flex-row gap-6\"\n            >\n              <motion.button\n                whileHover={{ scale: 1.05, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-primary\"\n              >\n                Our Story\n              </motion.button>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-secondary\"\n              >\n                Download Brochure\n              </motion.button>\n            </motion.div>\n          </motion.div>\n\n          {/* Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative overflow-hidden rounded-2xl luxury-shadow\">\n              <img\n                src=\"https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80\"\n                alt=\"Craftsman working on stone\"\n                className=\"w-full h-96 lg:h-[500px] object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\" />\n            </div>\n            \n            {/* Floating Stats Card */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              viewport={{ once: true }}\n              className=\"absolute -bottom-8 -left-8 bg-white rounded-xl p-6 luxury-shadow\"\n            >\n              <div className=\"text-center\">\n                <div className=\"font-display text-3xl font-bold text-purple-600 mb-1\">25+</div>\n                <div className=\"text-stone-600 font-medium\">Years of Excellence</div>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Features Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-24 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -5 }}\n              className=\"text-center group\"\n            >\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-6 group-hover:bg-purple-600 transition-colors duration-300\">\n                <feature.icon className=\"w-8 h-8 text-purple-600 group-hover:text-white transition-colors duration-300\" />\n              </div>\n              <h3 className=\"font-semibold text-xl text-stone-800 mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-stone-600 leading-relaxed\">\n                {feature.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,QAAQ;;IACZ,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IACvC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAK,CAAC;KAAI;IAC3D,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,MAAM,WAAW;QACf;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAQ,KAAK;QAAY,WAAU;;0BAE7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE;gBAAE;gBACX,WAAU;0BAEV,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,mHAAmH,CAAC;oBACxI;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;kEAAgE;;;;;;kEAGhF,6LAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;oDACX;kEAEC,6LAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;0DAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;0DACX;;;;;;0DAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;0DACX;;;;;;;;;;;;kDAOH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;oDAAM,GAAG,CAAC;gDAAE;gDACjC,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;0DAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAuD;;;;;;8DACtE,6LAAC;oDAAI,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAE1B,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;;+BAfjB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;AAuBhC;GAvMM;;QAEwB,4KAAA,CAAA,YAAS;QAK3B,+KAAA,CAAA,eAAY;QACN,+KAAA,CAAA,eAAY;;;KARxB;uCAyMS", "debugId": null}}, {"offset": {"line": 1475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/ProductsGallery.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Filter, ArrowRight } from 'lucide-react';\n\nconst ProductsGallery = () => {\n  const [activeFilter, setActiveFilter] = useState('all');\n\n  const categories = [\n    { id: 'all', name: 'All Products' },\n    { id: 'natural', name: 'Natural Stone' },\n    { id: 'cast', name: 'Cast Stone' },\n    { id: 'architectural', name: 'Architectural' },\n    { id: 'custom', name: 'Custom Designs' }\n  ];\n\n  const products = [\n    {\n      id: 1,\n      title: \"Limestone Columns\",\n      category: \"natural\",\n      image: \"https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Elegant limestone columns for classical architecture\"\n    },\n    {\n      id: 2,\n      title: \"Cast Stone Balustrades\",\n      category: \"cast\",\n      image: \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Handcrafted balustrades with intricate detailing\"\n    },\n    {\n      id: 3,\n      title: \"Architectural Cornices\",\n      category: \"architectural\",\n      image: \"https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Decorative cornices for building facades\"\n    },\n    {\n      id: 4,\n      title: \"Custom Medallions\",\n      category: \"custom\",\n      image: \"https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Bespoke medallions and decorative elements\"\n    },\n    {\n      id: 5,\n      title: \"Sandstone Facades\",\n      category: \"natural\",\n      image: \"https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Premium sandstone for exterior cladding\"\n    },\n    {\n      id: 6,\n      title: \"Cast Stone Capitals\",\n      category: \"cast\",\n      image: \"https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Ornate capitals for column tops\"\n    },\n    {\n      id: 7,\n      title: \"Window Surrounds\",\n      category: \"architectural\",\n      image: \"https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Elegant window frames and surrounds\"\n    },\n    {\n      id: 8,\n      title: \"Custom Sculptures\",\n      category: \"custom\",\n      image: \"https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      description: \"Artistic sculptures and decorative pieces\"\n    }\n  ];\n\n  const filteredProducts = activeFilter === 'all' \n    ? products \n    : products.filter(product => product.category === activeFilter);\n\n  return (\n    <section id=\"products\" className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-block mb-4\">\n            <span className=\"text-purple-600 font-semibold text-lg tracking-wide uppercase\">\n              Our Products\n            </span>\n            <div className=\"w-20 h-1 bg-purple-600 mt-2 mx-auto\"></div>\n          </div>\n          \n          <h2 className=\"font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6\">\n            Premium Stone\n            <span className=\"text-gradient block\">Collections</span>\n          </h2>\n          \n          <p className=\"text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            Discover our extensive range of natural and cast stone products, \n            each crafted with precision and attention to detail.\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category) => (\n            <motion.button\n              key={category.id}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => setActiveFilter(category.id)}\n              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${\n                activeFilter === category.id\n                  ? 'bg-purple-600 text-white shadow-lg'\n                  : 'bg-stone-100 text-stone-700 hover:bg-stone-200'\n              }`}\n            >\n              <span className=\"flex items-center space-x-2\">\n                <Filter className=\"w-4 h-4\" />\n                <span>{category.name}</span>\n              </span>\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Products Grid */}\n        <motion.div\n          layout\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\"\n        >\n          <AnimatePresence mode=\"wait\">\n            {filteredProducts.map((product, index) => (\n              <motion.div\n                key={product.id}\n                layout\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                whileHover={{ y: -10 }}\n                className=\"group cursor-pointer\"\n              >\n                <div className=\"bg-white rounded-xl overflow-hidden luxury-shadow hover:shadow-2xl transition-all duration-500\">\n                  {/* Image */}\n                  <div className=\"relative overflow-hidden\">\n                    <img\n                      src={product.image}\n                      alt={product.title}\n                      className=\"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                    \n                    {/* Overlay Content */}\n                    <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                      <motion.button\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        className=\"bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-3 text-white hover:bg-white/30 transition-colors duration-300\"\n                      >\n                        <ArrowRight className=\"w-6 h-6\" />\n                      </motion.button>\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"p-6\">\n                    <h3 className=\"font-semibold text-xl text-stone-800 mb-2 group-hover:text-purple-600 transition-colors duration-300\">\n                      {product.title}\n                    </h3>\n                    <p className=\"text-stone-600 leading-relaxed\">\n                      {product.description}\n                    </p>\n                    \n                    <div className=\"mt-4 flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300\">\n                      <span>Learn More</span>\n                      <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-12 text-white\">\n            <h3 className=\"font-display text-3xl md:text-4xl font-bold mb-4\">\n              Need Something Custom?\n            </h3>\n            <p className=\"text-xl text-purple-100 mb-8 max-w-2xl mx-auto\">\n              Our expert craftsmen can create bespoke stone elements tailored to your exact specifications.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-stone-50 transition-colors duration-300\"\n            >\n              Request Custom Quote\n            </motion.button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProductsGallery;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,kBAAkB;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;QAAe;QAClC;YAAE,IAAI;YAAW,MAAM;QAAgB;QACvC;YAAE,IAAI;YAAQ,MAAM;QAAa;QACjC;YAAE,IAAI;YAAiB,MAAM;QAAgB;QAC7C;YAAE,IAAI;YAAU,MAAM;QAAiB;KACxC;IAED,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,iBAAiB,QACtC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgE;;;;;;8CAGhF,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAG,WAAU;;gCAAkE;8CAE9E,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAGxC,6LAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAO1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,SAAS,IAAM,gBAAgB,SAAS,EAAE;4BAC1C,WAAW,CAAC,+DAA+D,EACzE,iBAAiB,SAAS,EAAE,GACxB,uCACA,kDACJ;sCAEF,cAAA,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAM,SAAS,IAAI;;;;;;;;;;;;2BAZjB,SAAS,EAAE;;;;;;;;;;8BAmBtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,MAAM;oBACN,WAAU;8BAEV,cAAA,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,MAAM;gCACN,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,YAAY;oCAAE,GAAG,CAAC;gCAAG;gCACrB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,YAAY;4DAAE,OAAO;wDAAI;wDACzB,UAAU;4DAAE,OAAO;wDAAI;wDACvB,WAAU;kEAEV,cAAA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAM5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAGtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;+BA1CvB,QAAQ,EAAE;;;;;;;;;;;;;;;8BAoDvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAG9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;oCAAM,GAAG,CAAC;gCAAE;gCACjC,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAzNM;KAAA;uCA2NS", "debugId": null}}, {"offset": {"line": 1951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/Portfolio.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, ChevronLeft, ChevronRight, MapPin, Calendar, User } from 'lucide-react';\n\nconst Portfolio = () => {\n  const [selectedProject, setSelectedProject] = useState<number | null>(null);\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n\n  const projects = [\n    {\n      id: 1,\n      title: \"Grand Cathedral Restoration\",\n      location: \"New York, NY\",\n      year: \"2023\",\n      client: \"Historic Preservation Society\",\n      category: \"Religious Architecture\",\n      description: \"Complete restoration of 19th-century cathedral featuring intricate cast stone details, flying buttresses, and ornate facades.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    },\n    {\n      id: 2,\n      title: \"Luxury Resort Complex\",\n      location: \"Miami, FL\",\n      year: \"2023\",\n      client: \"Oceanview Resorts\",\n      category: \"Hospitality\",\n      description: \"Modern resort featuring custom cast stone elements, decorative columns, and Mediterranean-inspired architectural details.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    },\n    {\n      id: 3,\n      title: \"Corporate Headquarters\",\n      location: \"Chicago, IL\",\n      year: \"2022\",\n      client: \"Fortune 500 Company\",\n      category: \"Commercial\",\n      description: \"Contemporary office building with sleek limestone facade, custom medallions, and modern architectural elements.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    },\n    {\n      id: 4,\n      title: \"Historic Mansion Renovation\",\n      location: \"Boston, MA\",\n      year: \"2022\",\n      client: \"Private Residence\",\n      category: \"Residential\",\n      description: \"Restoration of 18th-century mansion with period-accurate cast stone details, ornate balustrades, and decorative elements.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    },\n    {\n      id: 5,\n      title: \"University Campus Expansion\",\n      location: \"Philadelphia, PA\",\n      year: \"2021\",\n      client: \"Ivy League University\",\n      category: \"Educational\",\n      description: \"New academic building featuring traditional collegiate Gothic architecture with modern cast stone techniques.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    },\n    {\n      id: 6,\n      title: \"Art Museum Extension\",\n      location: \"Los Angeles, CA\",\n      year: \"2021\",\n      client: \"Metropolitan Art Museum\",\n      category: \"Cultural\",\n      description: \"Contemporary museum wing with innovative cast stone facade, sculptural elements, and artistic architectural features.\",\n      images: [\n        \"https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\",\n        \"https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80\"\n      ]\n    }\n  ];\n\n  const openLightbox = (projectId: number) => {\n    setSelectedProject(projectId);\n    setCurrentImageIndex(0);\n  };\n\n  const closeLightbox = () => {\n    setSelectedProject(null);\n    setCurrentImageIndex(0);\n  };\n\n  const nextImage = () => {\n    const project = projects.find(p => p.id === selectedProject);\n    if (project) {\n      setCurrentImageIndex((prev) => \n        prev === project.images.length - 1 ? 0 : prev + 1\n      );\n    }\n  };\n\n  const prevImage = () => {\n    const project = projects.find(p => p.id === selectedProject);\n    if (project) {\n      setCurrentImageIndex((prev) => \n        prev === 0 ? project.images.length - 1 : prev - 1\n      );\n    }\n  };\n\n  const selectedProjectData = projects.find(p => p.id === selectedProject);\n\n  return (\n    <section id=\"portfolio\" className=\"py-24 bg-stone-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-block mb-4\">\n            <span className=\"text-purple-600 font-semibold text-lg tracking-wide uppercase\">\n              Our Portfolio\n            </span>\n            <div className=\"w-20 h-1 bg-purple-600 mt-2 mx-auto\"></div>\n          </div>\n          \n          <h2 className=\"font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6\">\n            Architectural\n            <span className=\"text-gradient block\">Masterpieces</span>\n          </h2>\n          \n          <p className=\"text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            Explore our portfolio of exceptional projects that showcase our expertise \n            in creating timeless architectural elements.\n          </p>\n        </motion.div>\n\n        {/* Projects Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {projects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -10 }}\n              className=\"group cursor-pointer\"\n              onClick={() => openLightbox(project.id)}\n            >\n              <div className=\"bg-white rounded-xl overflow-hidden luxury-shadow hover:shadow-2xl transition-all duration-500\">\n                {/* Image */}\n                <div className=\"relative overflow-hidden h-64\">\n                  <img\n                    src={project.images[0]}\n                    alt={project.title}\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\" />\n                  \n                  {/* Category Badge */}\n                  <div className=\"absolute top-4 left-4\">\n                    <span className=\"bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      {project.category}\n                    </span>\n                  </div>\n                  \n                  {/* Overlay Info */}\n                  <div className=\"absolute bottom-4 left-4 right-4 text-white\">\n                    <h3 className=\"font-semibold text-xl mb-1\">{project.title}</h3>\n                    <div className=\"flex items-center space-x-4 text-sm opacity-90\">\n                      <span className=\"flex items-center space-x-1\">\n                        <MapPin className=\"w-4 h-4\" />\n                        <span>{project.location}</span>\n                      </span>\n                      <span className=\"flex items-center space-x-1\">\n                        <Calendar className=\"w-4 h-4\" />\n                        <span>{project.year}</span>\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <p className=\"text-stone-600 leading-relaxed mb-4\">\n                    {project.description.substring(0, 120)}...\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2 text-stone-500 text-sm\">\n                      <User className=\"w-4 h-4\" />\n                      <span>{project.client}</span>\n                    </div>\n                    <span className=\"text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300\">\n                      View Project\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n\n      {/* Lightbox */}\n      <AnimatePresence>\n        {selectedProject && selectedProjectData && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4\"\n            onClick={closeLightbox}\n          >\n            <motion.div\n              initial={{ scale: 0.8, opacity: 0 }}\n              animate={{ scale: 1, opacity: 1 }}\n              exit={{ scale: 0.8, opacity: 0 }}\n              className=\"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\"\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Header */}\n              <div className=\"p-6 border-b border-stone-200 flex items-center justify-between\">\n                <div>\n                  <h3 className=\"font-display text-2xl font-bold text-stone-800\">\n                    {selectedProjectData.title}\n                  </h3>\n                  <p className=\"text-stone-600\">{selectedProjectData.location} • {selectedProjectData.year}</p>\n                </div>\n                <button\n                  onClick={closeLightbox}\n                  className=\"p-2 hover:bg-stone-100 rounded-full transition-colors duration-200\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n              </div>\n\n              {/* Image Gallery */}\n              <div className=\"relative\">\n                <img\n                  src={selectedProjectData.images[currentImageIndex]}\n                  alt={selectedProjectData.title}\n                  className=\"w-full h-96 object-cover\"\n                />\n                \n                {/* Navigation Arrows */}\n                {selectedProjectData.images.length > 1 && (\n                  <>\n                    <button\n                      onClick={prevImage}\n                      className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white hover:bg-white/30 transition-colors duration-300\"\n                    >\n                      <ChevronLeft className=\"w-6 h-6\" />\n                    </button>\n                    <button\n                      onClick={nextImage}\n                      className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white hover:bg-white/30 transition-colors duration-300\"\n                    >\n                      <ChevronRight className=\"w-6 h-6\" />\n                    </button>\n                  </>\n                )}\n\n                {/* Image Indicators */}\n                {selectedProjectData.images.length > 1 && (\n                  <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2\">\n                    {selectedProjectData.images.map((_, index) => (\n                      <button\n                        key={index}\n                        onClick={() => setCurrentImageIndex(index)}\n                        className={`w-2 h-2 rounded-full transition-colors duration-200 ${\n                          index === currentImageIndex ? 'bg-white' : 'bg-white/50'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                )}\n              </div>\n\n              {/* Project Details */}\n              <div className=\"p-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"font-semibold text-lg text-stone-800 mb-2\">Project Description</h4>\n                    <p className=\"text-stone-600 leading-relaxed\">{selectedProjectData.description}</p>\n                  </div>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h5 className=\"font-medium text-stone-800\">Client</h5>\n                      <p className=\"text-stone-600\">{selectedProjectData.client}</p>\n                    </div>\n                    <div>\n                      <h5 className=\"font-medium text-stone-800\">Category</h5>\n                      <p className=\"text-stone-600\">{selectedProjectData.category}</p>\n                    </div>\n                    <div>\n                      <h5 className=\"font-medium text-stone-800\">Completion Year</h5>\n                      <p className=\"text-stone-600\">{selectedProjectData.year}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </section>\n  );\n};\n\nexport default Portfolio;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAMA,MAAM,YAAY;;IAChB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,eAAe,CAAC;QACpB,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,qBAAqB;IACvB;IAEA,MAAM,YAAY;QAChB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,IAAI,SAAS;YACX,qBAAqB,CAAC,OACpB,SAAS,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,IAAI,OAAO;QAEpD;IACF;IAEA,MAAM,YAAY;QAChB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,IAAI,SAAS;YACX,qBAAqB,CAAC,OACpB,SAAS,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO;QAEpD;IACF;IAEA,MAAM,sBAAsB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAExD,qBACE,6LAAC;QAAQ,IAAG;QAAY,WAAU;;0BAChC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgE;;;;;;kDAGhF,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,6LAAC;gCAAG,WAAU;;oCAAkE;kDAE9E,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAE,WAAU;0CAA2D;;;;;;;;;;;;kCAO1E,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,GAAG,CAAC;gCAAG;gCACrB,WAAU;gCACV,SAAS,IAAM,aAAa,QAAQ,EAAE;0CAEtC,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,QAAQ,MAAM,CAAC,EAAE;oDACtB,KAAK,QAAQ,KAAK;oDAClB,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;8DAGf,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;;;;;;8DAKrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA8B,QAAQ,KAAK;;;;;;sEACzD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,6LAAC;sFAAM,QAAQ,QAAQ;;;;;;;;;;;;8EAEzB,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,6MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,6LAAC;sFAAM,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;;wDACV,QAAQ,WAAW,CAAC,SAAS,CAAC,GAAG;wDAAK;;;;;;;8DAGzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,6LAAC;8EAAM,QAAQ,MAAM;;;;;;;;;;;;sEAEvB,6LAAC;4DAAK,WAAU;sEAAyF;;;;;;;;;;;;;;;;;;;;;;;;+BArD1G,QAAQ,EAAE;;;;;;;;;;;;;;;;0BAiEvB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,mBAAmB,qCAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;8BAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,MAAM;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAC/B,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0CAGjC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,oBAAoB,KAAK;;;;;;0DAE5B,6LAAC;gDAAE,WAAU;;oDAAkB,oBAAoB,QAAQ;oDAAC;oDAAI,oBAAoB,IAAI;;;;;;;;;;;;;kDAE1F,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,oBAAoB,MAAM,CAAC,kBAAkB;wCAClD,KAAK,oBAAoB,KAAK;wCAC9B,WAAU;;;;;;oCAIX,oBAAoB,MAAM,CAAC,MAAM,GAAG,mBACnC;;0DACE,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;oCAM7B,oBAAoB,MAAM,CAAC,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,oBAAoB,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,sBAClC,6LAAC;gDAEC,SAAS,IAAM,qBAAqB;gDACpC,WAAW,CAAC,oDAAoD,EAC9D,UAAU,oBAAoB,aAAa,eAC3C;+CAJG;;;;;;;;;;;;;;;;0CAYf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA4C;;;;;;8DAC1D,6LAAC;oDAAE,WAAU;8DAAkC,oBAAoB,WAAW;;;;;;;;;;;;sDAEhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAAkB,oBAAoB,MAAM;;;;;;;;;;;;8DAE3D,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAAkB,oBAAoB,QAAQ;;;;;;;;;;;;8DAE7D,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAAkB,oBAAoB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW7E;GAlUM;KAAA;uCAoUS", "debugId": null}}, {"offset": {"line": 2687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/Services.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { \n  Hammer, \n  Palette, \n  Building, \n  Wrench, \n  Shield, \n  Truck,\n  ArrowRight,\n  CheckCircle\n} from 'lucide-react';\n\nconst Services = () => {\n  const services = [\n    {\n      icon: Palette,\n      title: \"Custom Design\",\n      description: \"Bespoke architectural elements tailored to your vision and project requirements.\",\n      features: [\"3D Modeling\", \"CAD Drawings\", \"Material Selection\", \"Design Consultation\"],\n      color: \"from-purple-500 to-purple-700\"\n    },\n    {\n      icon: Hammer,\n      title: \"Expert Craftsmanship\",\n      description: \"Master artisans with decades of experience in traditional and modern techniques.\",\n      features: [\"Hand Carving\", \"Cast Stone Production\", \"Surface Finishing\", \"Quality Control\"],\n      color: \"from-blue-500 to-blue-700\"\n    },\n    {\n      icon: Building,\n      title: \"Installation Services\",\n      description: \"Professional installation ensuring perfect fit and long-lasting durability.\",\n      features: [\"Site Assessment\", \"Professional Installation\", \"Structural Integration\", \"Final Inspection\"],\n      color: \"from-green-500 to-green-700\"\n    },\n    {\n      icon: Wrench,\n      title: \"Restoration & Repair\",\n      description: \"Specialized restoration services for historic and heritage buildings.\",\n      features: [\"Damage Assessment\", \"Period-Accurate Restoration\", \"Structural Repairs\", \"Preservation Techniques\"],\n      color: \"from-orange-500 to-orange-700\"\n    },\n    {\n      icon: Shield,\n      title: \"Quality Assurance\",\n      description: \"Rigorous testing and quality control to ensure exceptional standards.\",\n      features: [\"Material Testing\", \"Weather Resistance\", \"Structural Integrity\", \"Lifetime Warranty\"],\n      color: \"from-red-500 to-red-700\"\n    },\n    {\n      icon: Truck,\n      title: \"Logistics & Delivery\",\n      description: \"Secure packaging and timely delivery to project sites worldwide.\",\n      features: [\"Custom Packaging\", \"Global Shipping\", \"Installation Support\", \"Project Coordination\"],\n      color: \"from-indigo-500 to-indigo-700\"\n    }\n  ];\n\n  const process = [\n    {\n      step: \"01\",\n      title: \"Consultation\",\n      description: \"Initial meeting to understand your vision, requirements, and project scope.\"\n    },\n    {\n      step: \"02\",\n      title: \"Design & Planning\",\n      description: \"Detailed design development with 3D modeling and technical specifications.\"\n    },\n    {\n      step: \"03\",\n      title: \"Production\",\n      description: \"Expert craftsmanship using premium materials and time-tested techniques.\"\n    },\n    {\n      step: \"04\",\n      title: \"Installation\",\n      description: \"Professional installation with attention to detail and structural integrity.\"\n    }\n  ];\n\n  return (\n    <section id=\"services\" className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-block mb-4\">\n            <span className=\"text-purple-600 font-semibold text-lg tracking-wide uppercase\">\n              Our Services\n            </span>\n            <div className=\"w-20 h-1 bg-purple-600 mt-2 mx-auto\"></div>\n          </div>\n          \n          <h2 className=\"font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6\">\n            Comprehensive\n            <span className=\"text-gradient block\">Solutions</span>\n          </h2>\n          \n          <p className=\"text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            From initial concept to final installation, we provide end-to-end services \n            for all your architectural stone needs.\n          </p>\n        </motion.div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          {services.map((service, index) => (\n            <motion.div\n              key={service.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              whileHover={{ y: -10, scale: 1.02 }}\n              className=\"group\"\n            >\n              <div className=\"bg-white rounded-xl p-8 luxury-shadow hover:shadow-2xl transition-all duration-500 border border-stone-100 h-full\">\n                {/* Icon */}\n                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl bg-gradient-to-r ${service.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  <service.icon className=\"w-8 h-8 text-white\" />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"font-semibold text-xl text-stone-800 mb-3 group-hover:text-purple-600 transition-colors duration-300\">\n                  {service.title}\n                </h3>\n                \n                <p className=\"text-stone-600 leading-relaxed mb-6\">\n                  {service.description}\n                </p>\n\n                {/* Features */}\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <motion.li\n                      key={feature}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.4, delay: (index * 0.1) + (featureIndex * 0.05) }}\n                      viewport={{ once: true }}\n                      className=\"flex items-center space-x-2 text-stone-600\"\n                    >\n                      <CheckCircle className=\"w-4 h-4 text-green-500 flex-shrink-0\" />\n                      <span className=\"text-sm\">{feature}</span>\n                    </motion.li>\n                  ))}\n                </ul>\n\n                {/* CTA */}\n                <motion.button\n                  whileHover={{ x: 5 }}\n                  className=\"flex items-center space-x-2 text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300\"\n                >\n                  <span>Learn More</span>\n                  <ArrowRight className=\"w-4 h-4\" />\n                </motion.button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Process Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-stone-50 rounded-2xl p-8 md:p-12\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"font-display text-3xl md:text-4xl font-bold text-stone-800 mb-4\">\n              Our Process\n            </h3>\n            <p className=\"text-lg text-stone-600 max-w-2xl mx-auto\">\n              A streamlined approach that ensures exceptional results from concept to completion.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {process.map((step, index) => (\n              <motion.div\n                key={step.step}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center relative\"\n              >\n                {/* Step Number */}\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-purple-600 text-white rounded-full font-bold text-xl mb-4\">\n                  {step.step}\n                </div>\n\n                {/* Connector Line */}\n                {index < process.length - 1 && (\n                  <div className=\"hidden lg:block absolute top-8 left-full w-full h-0.5 bg-purple-200 transform -translate-x-8\" />\n                )}\n\n                <h4 className=\"font-semibold text-lg text-stone-800 mb-2\">\n                  {step.title}\n                </h4>\n                \n                <p className=\"text-stone-600 text-sm leading-relaxed\">\n                  {step.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-12 text-white\">\n            <h3 className=\"font-display text-3xl md:text-4xl font-bold mb-4\">\n              Ready to Start Your Project?\n            </h3>\n            <p className=\"text-xl text-purple-100 mb-8 max-w-2xl mx-auto\">\n              Let our experts help you bring your architectural vision to life with premium stone craftsmanship.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                whileHover={{ scale: 1.05, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-stone-50 transition-colors duration-300\"\n              >\n                Get Free Consultation\n              </motion.button>\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors duration-300\"\n              >\n                View Portfolio\n              </motion.button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Services;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAcA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YACE,MAAM,2MAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAgB;gBAAsB;aAAsB;YACtF,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAgB;gBAAyB;gBAAqB;aAAkB;YAC3F,OAAO;QACT;QACA;YACE,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAmB;gBAA6B;gBAA0B;aAAmB;YACxG,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAqB;gBAA+B;gBAAsB;aAA0B;YAC/G,OAAO;QACT;QACA;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAsB;gBAAwB;aAAoB;YACjG,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAmB;gBAAwB;aAAuB;YACjG,OAAO;QACT;KACD;IAED,MAAM,UAAU;QACd;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgE;;;;;;8CAGhF,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,6LAAC;4BAAG,WAAU;;gCAAkE;8CAE9E,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;sCAGxC,6LAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAO1E,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAClC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAW,CAAC,8EAA8E,EAAE,QAAQ,KAAK,CAAC,6DAA6D,CAAC;kDAC3K,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAI1B,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAGhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,6LAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gDAER,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,AAAC,QAAQ,MAAQ,eAAe;gDAAM;gDAC1E,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAK,WAAU;kEAAW;;;;;;;+CARtB;;;;;;;;;;kDAcX,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,GAAG;wCAAE;wCACnB,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BA9CrB,QAAQ,KAAK;;;;;;;;;;8BAsDxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkE;;;;;;8CAGhF,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAK1D,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;wCAIX,QAAQ,QAAQ,MAAM,GAAG,mBACxB,6LAAC;4CAAI,WAAU;;;;;;sDAGjB,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAGb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;mCAtBd,KAAK,IAAI;;;;;;;;;;;;;;;;8BA8BtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAiD;;;;;;0CAG9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAhPM;uCAkPS", "debugId": null}}]}