'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Filter, ArrowRight } from 'lucide-react';

const ProductsGallery = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const categories = [
    { id: 'all', name: 'All Products' },
    { id: 'natural', name: 'Natural Stone' },
    { id: 'cast', name: 'Cast Stone' },
    { id: 'architectural', name: 'Architectural' },
    { id: 'custom', name: 'Custom Designs' }
  ];

  const products = [
    {
      id: 1,
      title: "Limestone Columns",
      category: "natural",
      image: "https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Elegant limestone columns for classical architecture"
    },
    {
      id: 2,
      title: "Cast Stone Balustrades",
      category: "cast",
      image: "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Handcrafted balustrades with intricate detailing"
    },
    {
      id: 3,
      title: "Architectural Cornices",
      category: "architectural",
      image: "https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Decorative cornices for building facades"
    },
    {
      id: 4,
      title: "Custom Medallions",
      category: "custom",
      image: "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Bespoke medallions and decorative elements"
    },
    {
      id: 5,
      title: "Sandstone Facades",
      category: "natural",
      image: "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Premium sandstone for exterior cladding"
    },
    {
      id: 6,
      title: "Cast Stone Capitals",
      category: "cast",
      image: "https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Ornate capitals for column tops"
    },
    {
      id: 7,
      title: "Window Surrounds",
      category: "architectural",
      image: "https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Elegant window frames and surrounds"
    },
    {
      id: 8,
      title: "Custom Sculptures",
      category: "custom",
      image: "https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      description: "Artistic sculptures and decorative pieces"
    }
  ];

  const filteredProducts = activeFilter === 'all' 
    ? products 
    : products.filter(product => product.category === activeFilter);

  return (
    <section id="products" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-block mb-4">
            <span className="text-purple-600 font-semibold text-lg tracking-wide uppercase">
              Our Products
            </span>
            <div className="w-20 h-1 bg-purple-600 mt-2 mx-auto"></div>
          </div>
          
          <h2 className="font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6">
            Premium Stone
            <span className="text-gradient block">Collections</span>
          </h2>
          
          <p className="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
            Discover our extensive range of natural and cast stone products, 
            each crafted with precision and attention to detail.
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category) => (
            <motion.button
              key={category.id}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setActiveFilter(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                activeFilter === category.id
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'bg-stone-100 text-stone-700 hover:bg-stone-200'
              }`}
            >
              <span className="flex items-center space-x-2">
                <Filter className="w-4 h-4" />
                <span>{category.name}</span>
              </span>
            </motion.button>
          ))}
        </motion.div>

        {/* Products Grid */}
        <motion.div
          layout
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8"
        >
          <AnimatePresence mode="wait">
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -10 }}
                className="group cursor-pointer"
              >
                <div className="bg-white rounded-xl overflow-hidden luxury-shadow hover:shadow-2xl transition-all duration-500">
                  {/* Image */}
                  <div className="relative overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.title}
                      className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    
                    {/* Overlay Content */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-3 text-white hover:bg-white/30 transition-colors duration-300"
                      >
                        <ArrowRight className="w-6 h-6" />
                      </motion.button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <h3 className="font-semibold text-xl text-stone-800 mb-2 group-hover:text-purple-600 transition-colors duration-300">
                      {product.title}
                    </h3>
                    <p className="text-stone-600 leading-relaxed">
                      {product.description}
                    </p>
                    
                    <div className="mt-4 flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300">
                      <span>Learn More</span>
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-purple-600 to-purple-800 rounded-2xl p-12 text-white">
            <h3 className="font-display text-3xl md:text-4xl font-bold mb-4">
              Need Something Custom?
            </h3>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Our expert craftsmen can create bespoke stone elements tailored to your exact specifications.
            </p>
            <motion.button
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-stone-50 transition-colors duration-300"
            >
              Request Custom Quote
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductsGallery;
