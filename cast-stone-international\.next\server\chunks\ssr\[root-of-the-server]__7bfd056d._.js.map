{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect, useRef } from \"react\";\nimport { motion, useScroll, useTransform, AnimatePresence } from \"framer-motion\";\nimport { ArrowRight, Play, Shield, Award, Menu, X, Star, Phone, Mail, MapPin, CheckCircle, Sparkles, Building, Hammer, Eye } from \"lucide-react\";\n\nexport default function Home() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [currentFeature, setCurrentFeature] = useState(0);\n\n\n  const heroRef = useRef<HTMLElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: heroRef,\n    offset: [\"start start\", \"end start\"]\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [\"0%\", \"50%\"]);\n  const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);\n\n  const features = [\n    {\n      icon: Building,\n      title: \"Architectural Design\",\n      description: \"Custom cast stone designs that bring your architectural vision to life\",\n      image: \"https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      color: \"from-purple-500 to-pink-500\"\n    },\n    {\n      icon: Hammer,\n      title: \"Expert Installation\",\n      description: \"Professional installation services with precision and attention to detail\",\n      image: \"https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      color: \"from-blue-500 to-purple-500\"\n    },\n    {\n      icon: Shield,\n      title: \"Quality Assurance\",\n      description: \"Rigorous quality control ensuring every piece meets our high standards\",\n      image: \"https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      color: \"from-green-500 to-blue-500\"\n    },\n    {\n      icon: Eye,\n      title: \"Heritage Restoration\",\n      description: \"Specialized restoration services preserving historical architectural beauty\",\n      image: \"https://images.unsplash.com/photo-1520637836862-4d197d17c93a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n      color: \"from-orange-500 to-red-500\"\n    }\n  ];\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    window.addEventListener(\"mousemove\", handleMouseMove);\n\n    return () => {\n      window.removeEventListener(\"scroll\", handleScroll);\n      window.removeEventListener(\"mousemove\", handleMouseMove);\n    };\n  }, []);\n\n  // Auto-rotate features\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentFeature((prev) => (prev === features.length - 1 ? 0 : prev + 1));\n    }, 4000);\n    return () => clearInterval(interval);\n  }, [features.length]);\n\n  return (\n    <div className=\"min-h-screen bg-white relative overflow-hidden\">\n      {/* Animated Background Elements */}\n      <div className=\"fixed inset-0 pointer-events-none\">\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"></div>\n        <div className=\"absolute top-40 right-10 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"></div>\n        <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000\"></div>\n      </div>\n\n      {/* Sleek Navigation */}\n      <motion.nav\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${\n          scrolled\n            ? 'bg-white/80 backdrop-blur-xl shadow-2xl border-b border-purple-100'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-20\">\n            <motion.div\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"flex items-center\"\n            >\n              <div className=\"relative\">\n                <h1 className=\"font-display text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  Cast Stone\n                </h1>\n                <span className=\"font-display text-2xl font-bold text-gray-800 ml-2\">International</span>\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                  className=\"absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full\"\n                />\n              </div>\n            </motion.div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden md:flex items-center space-x-8\">\n              {['Services', 'Projects', 'About', 'Contact'].map((item, index) => (\n                <motion.a\n                  key={item}\n                  href={`#${item.toLowerCase()}`}\n                  initial={{ opacity: 0, y: -20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.3 + index * 0.1 }}\n                  className=\"relative text-gray-700 hover:text-purple-600 transition-all duration-300 text-sm font-medium group\"\n                >\n                  {item}\n                  <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full\"></span>\n                </motion.a>\n              ))}\n\n              <motion.button\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.8 }}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-primary relative overflow-hidden group\"\n              >\n                <span className=\"relative z-10\">Get Quote</span>\n                <div className=\"absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              </motion.button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <motion.button\n                whileTap={{ scale: 0.95 }}\n                onClick={() => setIsMenuOpen(!isMenuOpen)}\n                className=\"text-gray-700 hover:text-purple-600 transition-colors p-2\"\n              >\n                <AnimatePresence mode=\"wait\">\n                  {isMenuOpen ? (\n                    <motion.div\n                      key=\"close\"\n                      initial={{ rotate: -90, opacity: 0 }}\n                      animate={{ rotate: 0, opacity: 1 }}\n                      exit={{ rotate: 90, opacity: 0 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      <X size={24} />\n                    </motion.div>\n                  ) : (\n                    <motion.div\n                      key=\"menu\"\n                      initial={{ rotate: 90, opacity: 0 }}\n                      animate={{ rotate: 0, opacity: 1 }}\n                      exit={{ rotate: -90, opacity: 0 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      <Menu size={24} />\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n              className=\"md:hidden bg-white/95 backdrop-blur-xl border-t border-purple-100\"\n            >\n              <div className=\"px-4 py-6 space-y-4\">\n                {['Services', 'Projects', 'About', 'Contact'].map((item, index) => (\n                  <motion.a\n                    key={item}\n                    href={`#${item.toLowerCase()}`}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={() => setIsMenuOpen(false)}\n                    className=\"block text-gray-700 hover:text-purple-600 transition-colors duration-300 text-sm font-medium py-2\"\n                  >\n                    {item}\n                  </motion.a>\n                ))}\n                <motion.button\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: 0.4 }}\n                  className=\"w-full btn-primary\"\n                >\n                  Get Quote\n                </motion.button>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </motion.nav>\n\n      <main>\n        {/* Dynamic Hero Section */}\n        <section ref={heroRef} id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n          {/* Parallax Background */}\n          <motion.div\n            style={{ y }}\n            className=\"absolute inset-0 z-0\"\n          >\n            <div\n              className=\"w-full h-[120%] bg-cover bg-center bg-no-repeat\"\n              style={{\n                backgroundImage: `linear-gradient(135deg, rgba(139, 92, 246, 0.9) 0%, rgba(124, 58, 237, 0.8) 50%, rgba(109, 40, 217, 0.9) 100%), url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`\n              }}\n            />\n          </motion.div>\n\n          {/* Floating Elements */}\n          <div className=\"absolute inset-0 pointer-events-none\">\n            {[...Array(6)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-2 h-2 bg-white rounded-full opacity-30\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                }}\n                animate={{\n                  y: [0, -30, 0],\n                  opacity: [0.3, 0.8, 0.3],\n                }}\n                transition={{\n                  duration: 3 + Math.random() * 2,\n                  repeat: Infinity,\n                  delay: Math.random() * 2,\n                }}\n              />\n            ))}\n          </div>\n\n          {/* Hero Content */}\n          <motion.div\n            style={{ opacity }}\n            className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\"\n          >\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 1, delay: 0.2 }}\n              className=\"max-w-5xl mx-auto\"\n            >\n              {/* Badge */}\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.5 }}\n                className=\"inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-8 border border-white/30\"\n              >\n                <Sparkles className=\"mr-2\" size={16} />\n                Premium Stone Solutions Since 1999\n              </motion.div>\n\n              {/* Main Heading */}\n              <motion.h1\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 1, delay: 0.3 }}\n                className=\"font-display text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight mb-8\"\n              >\n                Crafting\n                <motion.span\n                  initial={{ opacity: 0, x: -50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 1, delay: 0.8 }}\n                  className=\"block bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent\"\n                >\n                  Timeless\n                </motion.span>\n                <motion.span\n                  initial={{ opacity: 0, x: 50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 1, delay: 1.1 }}\n                  className=\"block\"\n                >\n                  Architecture\n                </motion.span>\n              </motion.h1>\n\n              {/* Subtitle */}\n              <motion.p\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 1, delay: 0.6 }}\n                className=\"text-xl md:text-2xl text-white/90 mb-12 leading-relaxed max-w-3xl mx-auto\"\n              >\n                Transform your vision into reality with our premium cast stone solutions.\n                Where traditional craftsmanship meets modern innovation.\n              </motion.p>\n\n              {/* CTA Buttons */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 1, delay: 0.9 }}\n                className=\"flex flex-col sm:flex-row gap-6 justify-center mb-16\"\n              >\n                <motion.button\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"group bg-white text-purple-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-50 transition-all duration-300 flex items-center justify-center shadow-2xl\"\n                >\n                  <Play className=\"mr-3 group-hover:scale-110 transition-transform\" size={24} />\n                  Explore Projects\n                  <ArrowRight className=\"ml-3 group-hover:translate-x-1 transition-transform\" size={24} />\n                </motion.button>\n\n                <motion.button\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"group border-2 border-white text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-purple-600 transition-all duration-300 flex items-center justify-center backdrop-blur-sm\"\n                >\n                  <Phone className=\"mr-3 group-hover:scale-110 transition-transform\" size={24} />\n                  Get Free Quote\n                </motion.button>\n              </motion.div>\n\n              {/* Stats */}\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 1, delay: 1.2 }}\n                className=\"grid grid-cols-3 gap-8 max-w-2xl mx-auto\"\n              >\n                {[\n                  { number: \"500+\", label: \"Projects Completed\" },\n                  { number: \"25+\", label: \"Years Experience\" },\n                  { number: \"98%\", label: \"Client Satisfaction\" }\n                ].map((stat, index) => (\n                  <motion.div\n                    key={stat.label}\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ delay: 1.4 + index * 0.2 }}\n                    className=\"text-center\"\n                  >\n                    <div className=\"text-3xl md:text-4xl font-bold text-white mb-2\">{stat.number}</div>\n                    <div className=\"text-white/80 text-sm\">{stat.label}</div>\n                  </motion.div>\n                ))}\n              </motion.div>\n            </motion.div>\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 2 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <motion.div\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n            >\n              <motion.div\n                animate={{ y: [0, 12, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n                className=\"w-1 h-3 bg-white rounded-full mt-2\"\n              />\n            </motion.div>\n          </motion.div>\n        </section>\n\n        {/* Dynamic Features Section */}\n        <section className=\"py-32 bg-gradient-to-br from-gray-50 to-purple-50 relative overflow-hidden\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-5\">\n            <div className=\"absolute inset-0\" style={{\n              backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238b5cf6' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            }} />\n          </div>\n\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n              className=\"text-center mb-20\"\n            >\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.2 }}\n                className=\"inline-flex items-center px-4 py-2 bg-purple-100 text-purple-600 rounded-full text-sm font-medium mb-6\"\n              >\n                <Sparkles className=\"mr-2\" size={16} />\n                Our Expertise\n              </motion.div>\n\n              <h2 className=\"text-5xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-6\">\n                Unify your construction needs\n              </h2>\n\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                From concept to completion, we provide comprehensive stone solutions that exceed expectations\n              </p>\n            </motion.div>\n\n            {/* Interactive Feature Showcase */}\n            <div className=\"grid lg:grid-cols-2 gap-16 items-center mb-20\">\n              {/* Feature Navigation */}\n              <div className=\"space-y-6\">\n                {features.map((feature, index) => (\n                  <motion.div\n                    key={feature.title}\n                    initial={{ opacity: 0, x: -30 }}\n                    whileInView={{ opacity: 1, x: 0 }}\n                    viewport={{ once: true }}\n                    transition={{ delay: index * 0.1 }}\n                    className={`p-6 rounded-2xl cursor-pointer transition-all duration-500 ${\n                      currentFeature === index\n                        ? 'bg-white shadow-2xl border-l-4 border-purple-500'\n                        : 'bg-white/50 hover:bg-white hover:shadow-lg'\n                    }`}\n                    onClick={() => setCurrentFeature(index)}\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <motion.div\n                        animate={{\n                          scale: currentFeature === index ? 1.1 : 1,\n                          rotate: currentFeature === index ? 360 : 0\n                        }}\n                        transition={{ duration: 0.5 }}\n                        className={`p-3 rounded-xl bg-gradient-to-r ${feature.color}`}\n                      >\n                        <feature.icon className=\"text-white\" size={24} />\n                      </motion.div>\n                      <div className=\"flex-1\">\n                        <h3 className=\"text-xl font-bold text-gray-900 mb-2\">{feature.title}</h3>\n                        <p className=\"text-gray-600\">{feature.description}</p>\n                      </div>\n                      <motion.div\n                        animate={{ opacity: currentFeature === index ? 1 : 0 }}\n                        className=\"text-purple-500\"\n                      >\n                        <ArrowRight size={20} />\n                      </motion.div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Feature Image Display */}\n              <div className=\"relative\">\n                <motion.div\n                  className=\"relative h-96 rounded-3xl overflow-hidden shadow-2xl\"\n                  layoutId=\"feature-image\"\n                >\n                  <AnimatePresence mode=\"wait\">\n                    <motion.img\n                      key={currentFeature}\n                      src={features[currentFeature].image}\n                      alt={features[currentFeature].title}\n                      initial={{ opacity: 0, scale: 1.1 }}\n                      animate={{ opacity: 1, scale: 1 }}\n                      exit={{ opacity: 0, scale: 0.9 }}\n                      transition={{ duration: 0.5 }}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </AnimatePresence>\n\n                  {/* Overlay */}\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n\n                  {/* Feature Badge */}\n                  <motion.div\n                    key={currentFeature}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"absolute bottom-6 left-6 right-6\"\n                  >\n                    <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl p-4\">\n                      <h4 className=\"font-bold text-gray-900 mb-1\">{features[currentFeature].title}</h4>\n                      <p className=\"text-gray-600 text-sm\">{features[currentFeature].description}</p>\n                    </div>\n                  </motion.div>\n                </motion.div>\n\n                {/* Floating Elements */}\n                <motion.div\n                  animate={{ y: [0, -10, 0] }}\n                  transition={{ duration: 3, repeat: Infinity }}\n                  className=\"absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full opacity-20\"\n                />\n                <motion.div\n                  animate={{ y: [0, 10, 0] }}\n                  transition={{ duration: 4, repeat: Infinity, delay: 1 }}\n                  className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-20\"\n                />\n              </div>\n            </div>\n\n            {/* Stats Grid */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n              className=\"grid md:grid-cols-4 gap-8\"\n            >\n              {[\n                { number: \"500+\", label: \"Projects Completed\", icon: Building },\n                { number: \"25+\", label: \"Years Experience\", icon: Award },\n                { number: \"98%\", label: \"Client Satisfaction\", icon: Star },\n                { number: \"24/7\", label: \"Support Available\", icon: Shield }\n              ].map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  viewport={{ once: true }}\n                  transition={{ delay: index * 0.1 }}\n                  whileHover={{ scale: 1.05 }}\n                  className=\"bg-white rounded-2xl p-6 text-center shadow-lg hover:shadow-xl transition-all duration-300\"\n                >\n                  <motion.div\n                    whileHover={{ rotate: 360 }}\n                    transition={{ duration: 0.5 }}\n                    className=\"inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl mb-4\"\n                  >\n                    <stat.icon className=\"text-white\" size={24} />\n                  </motion.div>\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">{stat.number}</div>\n                  <div className=\"text-gray-600\">{stat.label}</div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Portfolio Showcase */}\n        <section className=\"py-32 bg-white relative overflow-hidden\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n              className=\"text-center mb-20\"\n            >\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.2 }}\n                className=\"inline-flex items-center px-4 py-2 bg-purple-100 text-purple-600 rounded-full text-sm font-medium mb-6\"\n              >\n                <Building className=\"mr-2\" size={16} />\n                Our Portfolio\n              </motion.div>\n\n              <h2 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n                Featured\n                <span className=\"block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  Projects\n                </span>\n              </h2>\n\n              <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                Discover our portfolio of exceptional cast stone projects that showcase our commitment to quality and craftsmanship\n              </p>\n            </motion.div>\n\n            {/* Project Grid */}\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n              {[\n                {\n                  title: \"Modern Residential Complex\",\n                  category: \"Residential\",\n                  description: \"Luxury apartment complex featuring custom cast stone facades and architectural details.\",\n                  image: \"https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2024\",\n                  size: \"large\"\n                },\n                {\n                  title: \"Historic Cathedral Restoration\",\n                  category: \"Heritage\",\n                  description: \"Meticulous restoration of 19th-century cathedral stonework.\",\n                  image: \"https://images.unsplash.com/photo-1520637836862-4d197d17c93a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2023\",\n                  size: \"medium\"\n                },\n                {\n                  title: \"Corporate Headquarters\",\n                  category: \"Commercial\",\n                  description: \"Contemporary office building with striking stone architectural elements.\",\n                  image: \"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2024\",\n                  size: \"medium\"\n                },\n                {\n                  title: \"Luxury Hotel Facade\",\n                  category: \"Hospitality\",\n                  description: \"Elegant hotel entrance featuring custom carved stone elements.\",\n                  image: \"https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2023\",\n                  size: \"large\"\n                },\n                {\n                  title: \"University Campus\",\n                  category: \"Educational\",\n                  description: \"Classical stone architecture for prestigious university buildings.\",\n                  image: \"https://images.unsplash.com/photo-1562774053-701939374585?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2024\",\n                  size: \"medium\"\n                },\n                {\n                  title: \"Private Estate\",\n                  category: \"Residential\",\n                  description: \"Bespoke stone features for luxury private residence.\",\n                  image: \"https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80\",\n                  year: \"2023\",\n                  size: \"medium\"\n                }\n              ].map((project, index) => (\n                <motion.div\n                  key={project.title}\n                  initial={{ opacity: 0, y: 50 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                  whileHover={{ y: -10 }}\n                  className={`group cursor-pointer ${\n                    project.size === 'large' ? 'md:col-span-2' : ''\n                  }`}\n                >\n                  <div className=\"relative overflow-hidden rounded-3xl bg-white shadow-lg hover:shadow-2xl transition-all duration-500\">\n                    <div className={`relative overflow-hidden ${\n                      project.size === 'large' ? 'h-80' : 'h-64'\n                    }`}>\n                      <motion.img\n                        src={project.image}\n                        alt={project.title}\n                        className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n                        whileHover={{ scale: 1.1 }}\n                      />\n\n                      {/* Gradient Overlay */}\n                      <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\n\n                      {/* Category Badge */}\n                      <motion.div\n                        initial={{ opacity: 0, scale: 0.8 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ delay: 0.3 + index * 0.1 }}\n                        className=\"absolute top-4 left-4 px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-xs font-medium text-gray-700\"\n                      >\n                        {project.category}\n                      </motion.div>\n\n                      {/* Year Badge */}\n                      <div className=\"absolute top-4 right-4 px-3 py-1 bg-purple-500 text-white rounded-full text-xs font-medium\">\n                        {project.year}\n                      </div>\n\n                      {/* Hover Content */}\n                      <motion.div\n                        initial={{ opacity: 0, y: 20 }}\n                        whileHover={{ opacity: 1, y: 0 }}\n                        className=\"absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-all duration-500\"\n                      >\n                        <h4 className=\"text-xl font-bold mb-2\">{project.title}</h4>\n                        <p className=\"text-sm text-gray-200 mb-3\">{project.description}</p>\n                        <div className=\"flex items-center text-sm\">\n                          <ArrowRight className=\"mr-2\" size={16} />\n                          View Details\n                        </div>\n                      </motion.div>\n                    </div>\n\n                    {/* Card Content */}\n                    <div className=\"p-6\">\n                      <h4 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors\">\n                        {project.title}\n                      </h4>\n                      <p className=\"text-gray-600 text-sm leading-relaxed\">\n                        {project.description}\n                      </p>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* CTA */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n              className=\"text-center\"\n            >\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-primary text-lg px-8 py-4\"\n              >\n                View All Projects\n                <ArrowRight className=\"ml-2\" size={20} />\n              </motion.button>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Testimonials Section */}\n        <section className=\"py-32 bg-gradient-to-br from-purple-50 to-pink-50 relative overflow-hidden\">\n          {/* Background Elements */}\n          <div className=\"absolute inset-0 opacity-10\">\n            <div className=\"absolute top-20 left-10 w-40 h-40 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse\"></div>\n            <div className=\"absolute bottom-20 right-10 w-40 h-40 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000\"></div>\n          </div>\n\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n              className=\"text-center mb-20\"\n            >\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.2 }}\n                className=\"inline-flex items-center px-4 py-2 bg-white/80 backdrop-blur-sm text-purple-600 rounded-full text-sm font-medium mb-6 border border-purple-200\"\n              >\n                <Star className=\"mr-2\" size={16} />\n                Client Testimonials\n              </motion.div>\n\n              <h2 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n                Hear it from our\n                <span className=\"block bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  satisfied clients\n                </span>\n              </h2>\n            </motion.div>\n\n            {/* Testimonials Grid */}\n            <div className=\"max-w-5xl mx-auto\">\n              <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-12 mb-20\">\n                {[\n                  {\n                    name: \"Sarah Mitchell\",\n                    role: \"Project Manager\",\n                    content: \"Outstanding craftsmanship and attention to detail. The cast stone work exceeded our expectations.\",\n                    rating: 5,\n                    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80\"\n                  },\n                  {\n                    name: \"David Rodriguez\",\n                    role: \"Lead Architect\",\n                    content: \"Professional team that delivered on time. Would highly recommend for any stone project.\",\n                    rating: 5,\n                    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80\"\n                  },\n                  {\n                    name: \"Emma Thompson\",\n                    role: \"Conservation Officer\",\n                    content: \"Their expertise in heritage restoration is unmatched. They preserved the historical integrity perfectly.\",\n                    rating: 5,\n                    image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80\"\n                  }\n                ].map((testimonial, index) => (\n                <motion.div\n                  key={testimonial.name}\n                  initial={{ opacity: 0, y: 30 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  viewport={{ once: true }}\n                  transition={{ duration: 0.6, delay: index * 0.2 }}\n                  whileHover={{ y: -8, scale: 1.03 }}\n                  className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-10 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50\"\n                >\n                  {/* Rating Stars */}\n                  <div className=\"flex items-center justify-center mb-8\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <motion.div\n                        key={i}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ delay: 0.5 + i * 0.1 }}\n                      >\n                        <Star className=\"text-yellow-400 fill-current mx-1\" size={24} />\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  {/* Content */}\n                  <p className=\"text-gray-700 mb-10 leading-relaxed italic text-center text-lg\">\n                    \"{testimonial.content}\"\n                  </p>\n\n                  {/* Author */}\n                  <div className=\"flex flex-col items-center text-center\">\n                    <motion.img\n                      whileHover={{ scale: 1.1 }}\n                      src={testimonial.image}\n                      alt={testimonial.name}\n                      className=\"w-16 h-16 rounded-full object-cover mb-4\"\n                    />\n                    <div>\n                      <div className=\"font-bold text-gray-900 text-lg\">{testimonial.name}</div>\n                      <div className=\"text-purple-600 font-medium\">{testimonial.role}</div>\n                    </div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Final CTA Section */}\n        <section className=\"py-32 bg-gradient-to-br from-purple-600 to-pink-600 relative overflow-hidden\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-10\">\n            <div className=\"absolute inset-0\" style={{\n              backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            }} />\n          </div>\n\n          {/* Floating Elements */}\n          <div className=\"absolute inset-0 pointer-events-none\">\n            {[...Array(8)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-4 h-4 bg-white rounded-full opacity-20\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                }}\n                animate={{\n                  y: [0, -40, 0],\n                  opacity: [0.2, 0.6, 0.2],\n                  scale: [1, 1.2, 1],\n                }}\n                transition={{\n                  duration: 4 + Math.random() * 2,\n                  repeat: Infinity,\n                  delay: Math.random() * 2,\n                }}\n              />\n            ))}\n          </div>\n\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.8 }}\n            >\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.2 }}\n                className=\"inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-8 border border-white/30\"\n              >\n                <CheckCircle className=\"mr-2\" size={16} />\n                Ready to Start Your Project?\n              </motion.div>\n\n              <motion.h2\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.3 }}\n                className=\"text-5xl md:text-7xl font-bold text-white mb-8 leading-tight\"\n              >\n                500+ projects completed,\n                <span className=\"block text-yellow-300\">plus yours.</span>\n              </motion.h2>\n\n              <motion.p\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.4 }}\n                className=\"text-xl text-white/90 mb-12 max-w-2xl mx-auto leading-relaxed\"\n              >\n                Transform your architectural vision into reality. Get started with a free consultation today.\n              </motion.p>\n\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.5 }}\n                className=\"flex flex-col sm:flex-row gap-6 justify-center\"\n              >\n                <motion.button\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"group bg-white text-purple-600 px-8 py-4 rounded-2xl font-bold text-lg hover:bg-gray-50 transition-all duration-300 flex items-center justify-center shadow-2xl\"\n                >\n                  <Phone className=\"mr-3 group-hover:scale-110 transition-transform\" size={24} />\n                  Get Free Quote\n                  <ArrowRight className=\"ml-3 group-hover:translate-x-1 transition-transform\" size={24} />\n                </motion.button>\n\n                <motion.button\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                  className=\"group border-2 border-white text-white px-8 py-4 rounded-2xl font-bold text-lg hover:bg-white hover:text-purple-600 transition-all duration-300 flex items-center justify-center backdrop-blur-sm\"\n                >\n                  <Mail className=\"mr-3 group-hover:scale-110 transition-transform\" size={24} />\n                  Contact Us\n                </motion.button>\n              </motion.div>\n            </motion.div>\n          </div>\n        </section>\n\n        {/* Sleek Footer */}\n        <footer className=\"bg-gray-900 text-white relative overflow-hidden\">\n          {/* Background Pattern */}\n          <div className=\"absolute inset-0 opacity-5\">\n            <div className=\"absolute inset-0\" style={{\n              backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n            }} />\n          </div>\n\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative\">\n            {/* Main Footer Content */}\n            <div className=\"max-w-4xl mx-auto text-center mb-20\">\n              <motion.div\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ duration: 0.8 }}\n                className=\"mb-12\"\n              >\n                <h3 className=\"font-display text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-6\">\n                  Cast Stone International\n                </h3>\n                <p className=\"text-gray-400 leading-relaxed text-lg max-w-2xl mx-auto\">\n                  Crafting timeless architecture with premium cast stone solutions.\n                  Where traditional craftsmanship meets modern innovation.\n                </p>\n              </motion.div>\n\n              {/* Contact Info */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.3 }}\n                className=\"grid md:grid-cols-3 gap-8 mb-16\"\n              >\n                <div className=\"flex flex-col items-center\">\n                  <Phone className=\"text-purple-400 mb-3\" size={24} />\n                  <span className=\"text-gray-300\">+****************</span>\n                </div>\n                <div className=\"flex flex-col items-center\">\n                  <Mail className=\"text-purple-400 mb-3\" size={24} />\n                  <span className=\"text-gray-300\"><EMAIL></span>\n                </div>\n                <div className=\"flex flex-col items-center\">\n                  <MapPin className=\"text-purple-400 mb-3\" size={24} />\n                  <span className=\"text-gray-300\">123 Stone Avenue, Craftsman City</span>\n                </div>\n              </motion.div>\n\n              {/* Quick Links */}\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                viewport={{ once: true }}\n                transition={{ delay: 0.4 }}\n                className=\"flex flex-wrap justify-center gap-8 mb-16\"\n              >\n                {['Services', 'Portfolio', 'About Us', 'Contact', 'Careers'].map((item, index) => (\n                  <motion.a\n                    key={item}\n                    href=\"#\"\n                    initial={{ opacity: 0, y: 10 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    viewport={{ once: true }}\n                    transition={{ delay: 0.5 + index * 0.1 }}\n                    whileHover={{ scale: 1.05, color: '#a855f7' }}\n                    className=\"text-gray-400 hover:text-purple-400 transition-all duration-300 font-medium\"\n                  >\n                    {item}\n                  </motion.a>\n                ))}\n              </motion.div>\n            </div>\n\n            {/* Certifications */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.5 }}\n              className=\"flex flex-wrap justify-center items-center gap-8 mb-16 pb-8 border-b border-gray-800\"\n            >\n              {[\n                'ISO 9001 Certified',\n                'Licensed & Insured',\n                '25+ Years Experience'\n              ].map((cert, index) => (\n                <motion.div\n                  key={cert}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  viewport={{ once: true }}\n                  transition={{ delay: 0.6 + index * 0.2 }}\n                  whileHover={{ scale: 1.05 }}\n                  className=\"px-6 py-3 bg-gray-800 rounded-full text-sm text-gray-300 border border-gray-700 hover:border-purple-500 transition-all duration-300\"\n                >\n                  {cert}\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Bottom Footer */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.9 }}\n              className=\"flex flex-col md:flex-row justify-between items-center\"\n            >\n              <div className=\"flex items-center mb-4 md:mb-0\">\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  className=\"flex items-center\"\n                >\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg mr-3 flex items-center justify-center\">\n                    <Building className=\"text-white\" size={16} />\n                  </div>\n                  <span className=\"font-display text-lg font-bold text-white\">\n                    Cast Stone International\n                  </span>\n                </motion.div>\n              </div>\n\n              <div className=\"flex items-center space-x-6 text-gray-400 text-sm\">\n                <a href=\"#\" className=\"hover:text-purple-400 transition-colors\">Privacy Policy</a>\n                <a href=\"#\" className=\"hover:text-purple-400 transition-colors\">Terms of Service</a>\n                <span>© 2025 Cast Stone International. All rights reserved.</span>\n              </div>\n            </motion.div>\n          </div>\n        </footer>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAGrD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IAC7D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG;KAAE;IAE5D,MAAM,WAAW;QACf;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;QACT;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,aAAa;QAErC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,aAAa;QAC1C;IACF,GAAG,EAAE;IAEL,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,kBAAkB,CAAC,OAAU,SAAS,SAAS,MAAM,GAAG,IAAI,IAAI,OAAO;QACzE,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,WAAW,CAAC,4DAA4D,EACtE,WACI,uEACA,kBACJ;;kCAEF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6G;;;;;;0DAG3H,8OAAC;gDAAK,WAAU;0DAAqD;;;;;;0DACrE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,QAAQ;gDAAI;gDACvB,YAAY;oDAAE,UAAU;oDAAI,QAAQ;oDAAU,MAAM;gDAAS;gDAC7D,WAAU;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;wCACZ;4CAAC;4CAAY;4CAAY;4CAAS;yCAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDAEP,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,MAAM,QAAQ;gDAAI;gDACvC,WAAU;;oDAET;kEACD,8OAAC;wDAAK,WAAU;;;;;;;+CARX;;;;;sDAYT,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;8CAKnB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;4CAAC,MAAK;sDACnB,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,QAAQ,CAAC;oDAAI,SAAS;gDAAE;gDACnC,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,MAAM;oDAAE,QAAQ;oDAAI,SAAS;gDAAE;gDAC/B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,MAAM;;;;;;+CANL;;;;qEASN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,QAAQ;oDAAI,SAAS;gDAAE;gDAClC,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,MAAM;oDAAE,QAAQ,CAAC;oDAAI,SAAS;gDAAE;gDAChC,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;+CANR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBlB,8OAAC,yLAAA,CAAA,kBAAe;kCACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;oCACZ;wCAAC;wCAAY;wCAAY;wCAAS;qCAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CAEP,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;4CACjC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAET;2CARI;;;;;kDAWT,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASX,8OAAC;;kCAEC,8OAAC;wBAAQ,KAAK;wBAAS,IAAG;wBAAO,WAAU;;0CAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,OAAO;oCAAE;gCAAE;gCACX,WAAU;0CAEV,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,CAAC,gOAAgO,CAAC;oCACrP;;;;;;;;;;;0CAKJ,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4CAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAChC;wCACA,SAAS;4CACP,GAAG;gDAAC;gDAAG,CAAC;gDAAI;6CAAE;4CACd,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;wCAC1B;wCACA,YAAY;4CACV,UAAU,IAAI,KAAK,MAAM,KAAK;4CAC9B,QAAQ;4CACR,OAAO,KAAK,MAAM,KAAK;wCACzB;uCAdK;;;;;;;;;;0CAoBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,OAAO;oCAAE;gCAAQ;gCACjB,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;oCACtC,WAAU;;sDAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAKzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,OAAO;4CAAI;4CACtC,WAAU;;gDACX;8DAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAG,OAAO;oDAAI;oDACtC,WAAU;8DACX;;;;;;8DAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,UAAU;wDAAG,OAAO;oDAAI;oDACtC,WAAU;8DACX;;;;;;;;;;;;sDAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,OAAO;4CAAI;4CACtC,WAAU;sDACX;;;;;;sDAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,OAAO;4CAAI;4CACtC,WAAU;;8DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAkD,MAAM;;;;;;wDAAM;sEAE9E,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;4DAAsD,MAAM;;;;;;;;;;;;8DAGpF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAkD,MAAM;;;;;;wDAAM;;;;;;;;;;;;;sDAMnF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAG,OAAO;4CAAI;4CACtC,WAAU;sDAET;gDACC;oDAAE,QAAQ;oDAAQ,OAAO;gDAAqB;gDAC9C;oDAAE,QAAQ;oDAAO,OAAO;gDAAmB;gDAC3C;oDAAE,QAAQ;oDAAO,OAAO;gDAAsB;6CAC/C,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAI;oDAClC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDAAE,OAAO,MAAM,QAAQ;oDAAI;oDACvC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEAAkD,KAAK,MAAM;;;;;;sEAC5E,8OAAC;4DAAI,WAAU;sEAAyB,KAAK,KAAK;;;;;;;mDAP7C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0CAezB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAE;gCACvB,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAI;yCAAE;oCAAC;oCACzB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;8CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC;gDAAG;gDAAI;6CAAE;wCAAC;wCACzB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;wCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAmB,OAAO;wCACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oCACrR;;;;;;;;;;;0CAGF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;0DAIzC,8OAAC;gDAAG,WAAU;0DAAiH;;;;;;0DAI/H,8OAAC;gDAAE,WAAU;0DAA0C;;;;;;;;;;;;kDAMzD,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC9B,aAAa;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAChC,UAAU;4DAAE,MAAM;wDAAK;wDACvB,YAAY;4DAAE,OAAO,QAAQ;wDAAI;wDACjC,WAAW,CAAC,2DAA2D,EACrE,mBAAmB,QACf,qDACA,8CACJ;wDACF,SAAS,IAAM,kBAAkB;wDACjC,YAAY;4DAAE,OAAO;wDAAK;kEAE1B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,SAAS;wEACP,OAAO,mBAAmB,QAAQ,MAAM;wEACxC,QAAQ,mBAAmB,QAAQ,MAAM;oEAC3C;oEACA,YAAY;wEAAE,UAAU;oEAAI;oEAC5B,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,EAAE;8EAE7D,cAAA,8OAAC,QAAQ,IAAI;wEAAC,WAAU;wEAAa,MAAM;;;;;;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAwC,QAAQ,KAAK;;;;;;sFACnE,8OAAC;4EAAE,WAAU;sFAAiB,QAAQ,WAAW;;;;;;;;;;;;8EAEnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,SAAS;wEAAE,SAAS,mBAAmB,QAAQ,IAAI;oEAAE;oEACrD,WAAU;8EAEV,cAAA,8OAAC,kNAAA,CAAA,aAAU;wEAAC,MAAM;;;;;;;;;;;;;;;;;uDAhCjB,QAAQ,KAAK;;;;;;;;;;0DAwCxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,UAAS;;0EAET,8OAAC,yLAAA,CAAA,kBAAe;gEAAC,MAAK;0EACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEAET,KAAK,QAAQ,CAAC,eAAe,CAAC,KAAK;oEACnC,KAAK,QAAQ,CAAC,eAAe,CAAC,KAAK;oEACnC,SAAS;wEAAE,SAAS;wEAAG,OAAO;oEAAI;oEAClC,SAAS;wEAAE,SAAS;wEAAG,OAAO;oEAAE;oEAChC,MAAM;wEAAE,SAAS;wEAAG,OAAO;oEAAI;oEAC/B,YAAY;wEAAE,UAAU;oEAAI;oEAC5B,WAAU;mEAPL;;;;;;;;;;0EAYT,8OAAC;gEAAI,WAAU;;;;;;0EAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,WAAU;0EAEV,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAgC,QAAQ,CAAC,eAAe,CAAC,KAAK;;;;;;sFAC5E,8OAAC;4EAAE,WAAU;sFAAyB,QAAQ,CAAC,eAAe,CAAC,WAAW;;;;;;;;;;;;+DAPvE;;;;;;;;;;;kEAaT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,GAAG;gEAAC;gEAAG,CAAC;gEAAI;6DAAE;wDAAC;wDAC1B,YAAY;4DAAE,UAAU;4DAAG,QAAQ;wDAAS;wDAC5C,WAAU;;;;;;kEAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,GAAG;gEAAC;gEAAG;gEAAI;6DAAE;wDAAC;wDACzB,YAAY;4DAAE,UAAU;4DAAG,QAAQ;4DAAU,OAAO;wDAAE;wDACtD,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAET;4CACC;gDAAE,QAAQ;gDAAQ,OAAO;gDAAsB,MAAM,0MAAA,CAAA,WAAQ;4CAAC;4CAC9D;gDAAE,QAAQ;gDAAO,OAAO;gDAAoB,MAAM,oMAAA,CAAA,QAAK;4CAAC;4CACxD;gDAAE,QAAQ;gDAAO,OAAO;gDAAuB,MAAM,kMAAA,CAAA,OAAI;4CAAC;4CAC1D;gDAAE,QAAQ;gDAAQ,OAAO;gDAAqB,MAAM,sMAAA,CAAA,SAAM;4CAAC;yCAC5D,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,WAAU;;kEAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,QAAQ;wDAAI;wDAC1B,YAAY;4DAAE,UAAU;wDAAI;wDAC5B,WAAU;kEAEV,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;kEAAyC,KAAK,MAAM;;;;;;kEACnE,8OAAC;wDAAI,WAAU;kEAAiB,KAAK,KAAK;;;;;;;+CAhBrC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAwBzB,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAIzC,8OAAC;4CAAG,WAAU;;gDAAoD;8DAEhE,8OAAC;oDAAK,WAAU;8DAAmF;;;;;;;;;;;;sDAKrG,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,UAAU;4CACV,aAAa;4CACb,OAAO;4CACP,MAAM;4CACN,MAAM;wCACR;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO,QAAQ;4CAAI;4CAChD,YAAY;gDAAE,GAAG,CAAC;4CAAG;4CACrB,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,IAAI,KAAK,UAAU,kBAAkB,IAC7C;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,yBAAyB,EACxC,QAAQ,IAAI,KAAK,UAAU,SAAS,QACpC;;0EACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,KAAK,QAAQ,KAAK;gEAClB,KAAK,QAAQ,KAAK;gEAClB,WAAU;gEACV,YAAY;oEAAE,OAAO;gEAAI;;;;;;0EAI3B,8OAAC;gEAAI,WAAU;;;;;;0EAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,OAAO;gEAAI;gEAClC,aAAa;oEAAE,SAAS;oEAAG,OAAO;gEAAE;gEACpC,YAAY;oEAAE,OAAO,MAAM,QAAQ;gEAAI;gEACvC,WAAU;0EAET,QAAQ,QAAQ;;;;;;0EAInB,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI;;;;;;0EAIf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,YAAY;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC/B,WAAU;;kFAEV,8OAAC;wEAAG,WAAU;kFAA0B,QAAQ,KAAK;;;;;;kFACrD,8OAAC;wEAAE,WAAU;kFAA8B,QAAQ,WAAW;;;;;;kFAC9D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;gFAAO,MAAM;;;;;;4EAAM;;;;;;;;;;;;;;;;;;;kEAO/C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2CA5DrB,QAAQ,KAAK;;;;;;;;;;8CAqExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;8CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;4CACX;0DAEC,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;gDAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;wDAAO,MAAM;;;;;;oDAAM;;;;;;;0DAIrC,8OAAC;gDAAG,WAAU;;oDAAoD;kEAEhE,8OAAC;wDAAK,WAAU;kEAAmF;;;;;;;;;;;;;;;;;;kDAOvG,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ;gDACC;oDACE,MAAM;oDACN,MAAM;oDACN,SAAS;oDACT,QAAQ;oDACR,OAAO;gDACT;gDACA;oDACE,MAAM;oDACN,MAAM;oDACN,SAAS;oDACT,QAAQ;oDACR,OAAO;gDACT;gDACA;oDACE,MAAM;oDACN,MAAM;oDACN,SAAS;oDACT,QAAQ;oDACR,OAAO;gDACT;6CACD,CAAC,GAAG,CAAC,CAAC,aAAa,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,UAAU;wDAAE,MAAM;oDAAK;oDACvB,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,YAAY;wDAAE,GAAG,CAAC;wDAAG,OAAO;oDAAK;oDACjC,WAAU;;sEAGV,8OAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM,YAAY,MAAM;6DAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEAET,SAAS;wEAAE,SAAS;wEAAG,OAAO;oEAAE;oEAChC,aAAa;wEAAE,SAAS;wEAAG,OAAO;oEAAE;oEACpC,YAAY;wEAAE,OAAO,MAAM,IAAI;oEAAI;8EAEnC,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;wEAAoC,MAAM;;;;;;mEALrD;;;;;;;;;;sEAWX,8OAAC;4DAAE,WAAU;;gEAAiE;gEAC1E,YAAY,OAAO;gEAAC;;;;;;;sEAIxB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,YAAY;wEAAE,OAAO;oEAAI;oEACzB,KAAK,YAAY,KAAK;oEACtB,KAAK,YAAY,IAAI;oEACrB,WAAU;;;;;;8EAEZ,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAmC,YAAY,IAAI;;;;;;sFAClE,8OAAC;4EAAI,WAAU;sFAA+B,YAAY,IAAI;;;;;;;;;;;;;;;;;;;mDArC7D,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgD/B,8OAAC;wBAAQ,WAAU;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAmB,OAAO;wCACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oCACrR;;;;;;;;;;;0CAIF,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4CAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAChC;wCACA,SAAS;4CACP,GAAG;gDAAC;gDAAG,CAAC;gDAAI;6CAAE;4CACd,SAAS;gDAAC;gDAAK;gDAAK;6CAAI;4CACxB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB;wCACA,YAAY;4CACV,UAAU,IAAI,KAAK,MAAM,KAAK;4CAC9B,QAAQ;4CACR,OAAO,KAAK,MAAM,KAAK;wCACzB;uCAfK;;;;;;;;;;0CAoBX,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;;8DAEV,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;sDAI5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;;gDACX;8DAEC,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAG1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4CACP,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;sDACX;;;;;;sDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,OAAO;4CAAI;4CACzB,WAAU;;8DAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;;sEAEV,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAAkD,MAAM;;;;;;wDAAM;sEAE/E,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;4DAAsD,MAAM;;;;;;;;;;;;8DAGpF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oDACZ,YAAY;wDAAE,OAAO;wDAAM,GAAG,CAAC;oDAAE;oDACjC,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;4DAAkD,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASxF,8OAAC;wBAAO,WAAU;;0CAEhB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAmB,OAAO;wCACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oCACrR;;;;;;;;;;;0CAGF,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEAAkH;;;;;;kEAGhI,8OAAC;wDAAE,WAAU;kEAA0D;;;;;;;;;;;;0DAOzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;gEAAuB,MAAM;;;;;;0EAC9C,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;gEAAuB,MAAM;;;;;;0EAC7C,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;gEAAuB,MAAM;;;;;;0EAC/C,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAKpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;0DAET;oDAAC;oDAAY;oDAAa;oDAAY;oDAAW;iDAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wDAEP,MAAK;wDACL,SAAS;4DAAE,SAAS;4DAAG,GAAG;wDAAG;wDAC7B,aAAa;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAChC,UAAU;4DAAE,MAAM;wDAAK;wDACvB,YAAY;4DAAE,OAAO,MAAM,QAAQ;wDAAI;wDACvC,YAAY;4DAAE,OAAO;4DAAM,OAAO;wDAAU;wDAC5C,WAAU;kEAET;uDATI;;;;;;;;;;;;;;;;kDAgBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDAET;4CACC;4CACA;4CACA;yCACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,UAAU;oDAAE,MAAM;gDAAK;gDACvB,YAAY;oDAAE,OAAO,MAAM,QAAQ;gDAAI;gDACvC,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,WAAU;0DAET;+CARI;;;;;;;;;;kDAcX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAAa,MAAM;;;;;;;;;;;sEAEzC,8OAAC;4DAAK,WAAU;sEAA4C;;;;;;;;;;;;;;;;;0DAMhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAA0C;;;;;;kEAChE,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAA0C;;;;;;kEAChE,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}]}