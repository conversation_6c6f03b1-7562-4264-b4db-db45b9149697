# Cast Stone International - Architectural Excellence

A premium interior design and architectural stones website built with Next.js, TypeScript, and modern web technologies. This project showcases luxury stone craftsmanship with a magazine-inspired aesthetic and dynamic animations.

![Cast Stone International](https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80)

## 🌟 Features

### 🎨 **Design & UI/UX**
- **Luxury Magazine-Inspired Aesthetic**: Stone-inspired neutral tones with purple accents
- **Premium Typography**: Playfair Display for headings, Inter for body text
- **Dynamic Animations**: Framer Motion for smooth transitions and micro-interactions
- **Responsive Design**: Fully responsive across desktop, tablet, and mobile devices

### 🧩 **Components**
- **Navigation Bar**: Sophisticated navigation with smooth animations and mobile menu
- **Hero Section**: Dynamic background with video toggle and animated overlays
- **About Section**: Parallax effects and animated content reveals
- **Products Gallery**: Interactive filtering system with hover animations
- **Portfolio**: Stunning project grid with lightbox functionality
- **Services**: Animated service cards with process timeline
- **Contact**: Interactive form with validation and contact information
- **Footer**: Comprehensive company information with social links

### ⚡ **Performance Optimizations**
- **Image Optimization**: Next.js Image component with WebP/AVIF support
- **Lazy Loading**: Custom OptimizedImage component with loading states
- **Performance Monitoring**: Core Web Vitals tracking
- **SEO Optimization**: Complete metadata, Open Graph, and Twitter cards
- **Compression**: Enabled gzip compression and optimized caching

## 🛠️ **Tech Stack**

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom CSS variables
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Fonts**: Google Fonts (Inter, Playfair Display)
- **Performance**: Built-in Next.js optimizations

## 🚀 **Getting Started**

### Prerequisites
- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/umerfarooqlaghari/Patricks-website.git
   cd Patricks-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the website.

## 📁 **Project Structure**

```
├── src/
│   ├── app/
│   │   ├── globals.css          # Global styles and CSS variables
│   │   ├── layout.tsx           # Root layout with metadata
│   │   ├── loading.tsx          # Loading component
│   │   └── page.tsx             # Main page component
│   └── components/
│       ├── About.tsx            # About section with parallax
│       ├── Contact.tsx          # Contact form and information
│       ├── Footer.tsx           # Footer with links and social
│       ├── Hero.tsx             # Hero section with video
│       ├── Navigation.tsx       # Navigation bar
│       ├── OptimizedImage.tsx   # Custom image component
│       ├── PerformanceMonitor.tsx # Web vitals monitoring
│       ├── Portfolio.tsx        # Portfolio with lightbox
│       ├── ProductsGallery.tsx  # Products with filtering
│       └── Services.tsx         # Services with animations
├── public/                      # Static assets
├── next.config.ts              # Next.js configuration
├── tailwind.config.ts          # Tailwind CSS configuration
└── tsconfig.json               # TypeScript configuration
```
