/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-border-style: solid;
      --tw-tracking: initial;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
    }
  }
}

.row-start-2 {
  grid-row-start: 2;
}

.row-start-3 {
  grid-row-start: 3;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.min-h-screen {
  min-height: 100vh;
}

.w-full {
  width: 100%;
}

.list-inside {
  list-style-position: inside;
}

.list-decimal {
  list-style-type: decimal;
}

.grid-rows-\[20px_1fr_20px\] {
  grid-template-rows: 20px 1fr 20px;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-items-center {
  justify-items: center;
}

.gap-\[24px\] {
  gap: 24px;
}

.gap-\[32px\] {
  gap: 32px;
}

.rounded-full {
  border-radius: 3.40282e38px;
}

.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}

.border-solid {
  --tw-border-style: solid;
  border-style: solid;
}

.border-transparent {
  border-color: #0000;
}

.text-center {
  text-align: center;
}

.font-\[family-name\:var\(--font-geist-mono\)\] {
  font-family: var(--font-geist-mono);
}

.font-\[family-name\:var\(--font-geist-sans\)\] {
  font-family: var(--font-geist-sans);
}

.tracking-\[-\.01em\] {
  --tw-tracking: -.01em;
  letter-spacing: -.01em;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.transition-colors {
  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
  transition-timing-function: var(--tw-ease, ease);
  transition-duration: var(--tw-duration, 0s);
}

@media (hover: hover) {
  .hover\:border-transparent:hover {
    border-color: #0000;
  }
}

@media (hover: hover) {
  .hover\:bg-\[\#383838\]:hover {
    background-color: #383838;
  }
}

@media (hover: hover) {
  .hover\:bg-\[\#f2f2f2\]:hover {
    background-color: #f2f2f2;
  }
}

@media (hover: hover) {
  .hover\:underline:hover {
    text-decoration-line: underline;
  }
}

@media (hover: hover) {
  .hover\:underline-offset-4:hover {
    text-underline-offset: 4px;
  }
}

@media (prefers-color-scheme: dark) {
  .dark\:invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }
}

@media (prefers-color-scheme: dark) {
  @media (hover: hover) {
    .dark\:hover\:bg-\[\#1a1a1a\]:hover {
      background-color: #1a1a1a;
    }
  }
}

@media (prefers-color-scheme: dark) {
  @media (hover: hover) {
    .dark\:hover\:bg-\[\#ccc\]:hover {
      background-color: #ccc;
    }
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: #1f2937;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #fff;
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

.font-display {
  font-family: Playfair Display, serif;
}

.font-body {
  font-family: Inter, sans-serif;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f8fafc;
}

::-webkit-scrollbar-thumb {
  background: #8b5cf6;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #7c3aed;
}

.gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
}

.gradient-purple-light {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.glass {
  backdrop-filter: blur(10px);
  background: #ffffff1a;
  border: 1px solid #fff3;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-20px);
  }
}

.float {
  animation: 6s ease-in-out infinite float;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px #8b5cf64d;
  }

  50% {
    box-shadow: 0 0 40px #8b5cf699;
  }
}

.pulse-glow {
  animation: 2s ease-in-out infinite pulse-glow;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

.shimmer {
  position: relative;
  overflow: hidden;
}

.shimmer:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff6, #0000);
  width: 100%;
  height: 100%;
  animation: 2s infinite shimmer;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateX(-100%);
}

.transition-all {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.btn-primary {
  color: #fff;
  cursor: pointer;
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all .3s;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px #8b5cf64d;
}

.btn-secondary {
  color: #8b5cf6;
  cursor: pointer;
  background: none;
  border: 2px solid #8b5cf6;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all .3s;
}

.btn-secondary:hover {
  color: #fff;
  background: #8b5cf6;
  transform: translateY(-2px);
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-tracking {
  syntax: "*";
  inherits: false
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/