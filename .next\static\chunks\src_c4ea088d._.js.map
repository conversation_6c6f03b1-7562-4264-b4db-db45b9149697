{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Menu, X } from 'lucide-react';\n\nconst Navigation = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Home', href: '#home' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Services', href: '#services' },\n    { name: 'About', href: '#about' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <motion.nav\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        scrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-stone-200' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-20\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.2 }}\n            className=\"flex items-center\"\n          >\n            <h1 className=\"font-display text-2xl font-bold text-stone-800\">\n              Cast Stone\n              <span className=\"text-accent-primary ml-2\">International</span>\n            </h1>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 + index * 0.1 }}\n                className=\"font-body text-stone-700 hover:text-accent-primary transition-colors duration-300 relative group\"\n              >\n                {item.name}\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-accent-primary transition-all duration-300 group-hover:w-full\"></span>\n              </motion.a>\n            ))}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.8 }}\n              className=\"bg-accent-primary text-white px-6 py-2 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300\"\n            >\n              Get Quote\n            </motion.button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-stone-700 hover:text-accent-primary transition-colors\"\n            >\n              {isOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-white/95 backdrop-blur-md border-t border-stone-200\"\n          >\n            <div className=\"px-4 py-6 space-y-4\">\n              {navItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                  onClick={() => setIsOpen(false)}\n                  className=\"block font-body text-stone-700 hover:text-accent-primary transition-colors duration-300\"\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n              <motion.button\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: navItems.length * 0.1 }}\n                className=\"w-full bg-accent-primary text-white px-6 py-3 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300\"\n              >\n                Get Quote\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAC,4DAA4D,EACtE,WACI,qEACA,kBACJ;;0BAEF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,6LAAC;gCAAG,WAAU;;oCAAiD;kDAE7D,6LAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;;;;;;sCAK/C,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wCAEP,MAAM,KAAK,IAAI;wCACf,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,MAAM,QAAQ;wCAAI;wCACvC,WAAU;;4CAET,KAAK,IAAI;0DACV,6LAAC;gDAAK,WAAU;;;;;;;uCARX,KAAK,IAAI;;;;;8CAWlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhD,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAET,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;0CAWlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,SAAS,MAAM,GAAG;gCAAI;gCAC3C,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxHM;KAAA;uCA0HS", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ArrowR<PERSON>, Play } from 'lucide-react';\n\nconst HeroSection = () => {\n  return (\n    <section id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image with Overlay */}\n      <div className=\"absolute inset-0 z-0\">\n        <div \n          className=\"w-full h-full bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: `linear-gradient(rgba(28, 25, 23, 0.4), rgba(28, 25, 23, 0.6)), url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\"><defs><pattern id=\"stone\" patternUnits=\"userSpaceOnUse\" width=\"100\" height=\"100\"><rect width=\"100\" height=\"100\" fill=\"%23f5f5f4\"/><path d=\"M0 0h100v100H0z\" fill=\"none\" stroke=\"%23e7e5e4\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"1200\" height=\"800\" fill=\"url(%23stone)\"/></svg>')`\n          }}\n        />\n        {/* Floating geometric shapes */}\n        <motion.div\n          animate={{ \n            y: [0, -20, 0],\n            rotate: [0, 5, 0]\n          }}\n          transition={{ \n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-20 right-20 w-32 h-32 bg-accent-tertiary/20 rounded-full blur-xl\"\n        />\n        <motion.div\n          animate={{ \n            y: [0, 15, 0],\n            rotate: [0, -3, 0]\n          }}\n          transition={{ \n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 2\n          }}\n          className=\"absolute bottom-32 left-16 w-24 h-24 bg-accent-primary/30 rounded-lg blur-lg\"\n        />\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Column - Text Content */}\n          <div className=\"text-left\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-6\"\n            >\n              <span className=\"inline-block px-4 py-2 bg-accent-primary/10 text-accent-primary rounded-full text-sm font-medium mb-4\">\n                Premium Stone Solutions\n              </span>\n              <h1 className=\"font-display text-5xl lg:text-7xl font-bold text-white leading-tight\">\n                Crafting\n                <span className=\"block text-accent-tertiary\">Timeless</span>\n                <span className=\"block\">Architecture</span>\n              </h1>\n            </motion.div>\n\n            <motion.p\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"text-xl text-stone-200 mb-8 leading-relaxed max-w-lg\"\n            >\n              Transform your vision into reality with our premium cast stone solutions. \n              Where traditional craftsmanship meets modern innovation.\n            </motion.p>\n\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4\"\n            >\n              <button className=\"group bg-accent-primary text-white px-8 py-4 rounded-full font-medium hover:bg-accent-secondary transition-all duration-300 flex items-center justify-center\">\n                Explore Projects\n                <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" size={20} />\n              </button>\n              <button className=\"group border-2 border-white text-white px-8 py-4 rounded-full font-medium hover:bg-white hover:text-stone-800 transition-all duration-300 flex items-center justify-center\">\n                <Play className=\"mr-2\" size={20} />\n                Watch Story\n              </button>\n            </motion.div>\n\n            {/* Stats */}\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-white/20\"\n            >\n              <div>\n                <div className=\"text-3xl font-bold text-accent-tertiary\">500+</div>\n                <div className=\"text-stone-300 text-sm\">Projects Completed</div>\n              </div>\n              <div>\n                <div className=\"text-3xl font-bold text-accent-tertiary\">25+</div>\n                <div className=\"text-stone-300 text-sm\">Years Experience</div>\n              </div>\n              <div>\n                <div className=\"text-3xl font-bold text-accent-tertiary\">98%</div>\n                <div className=\"text-stone-300 text-sm\">Client Satisfaction</div>\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Right Column - Visual Element */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 1, delay: 0.3 }}\n            className=\"relative\"\n          >\n            <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden\">\n              <div \n                className=\"w-full h-full bg-cover bg-center\"\n                style={{\n                  backgroundImage: `url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 500\"><rect width=\"400\" height=\"500\" fill=\"%23a8a29e\"/><rect x=\"50\" y=\"50\" width=\"300\" height=\"200\" fill=\"%23d6d3d1\" rx=\"10\"/><rect x=\"80\" y=\"80\" width=\"240\" height=\"140\" fill=\"%23f5f5f4\" rx=\"5\"/><circle cx=\"200\" cy=\"350\" r=\"80\" fill=\"%238b5a3c\"/><rect x=\"160\" y=\"310\" width=\"80\" height=\"80\" fill=\"%236b4423\" rx=\"40\"/></svg>')`\n                }}\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-stone-900/50 to-transparent\" />\n              \n              {/* Floating UI Elements */}\n              <motion.div\n                animate={{ y: [0, -10, 0] }}\n                transition={{ duration: 3, repeat: Infinity }}\n                className=\"absolute top-8 right-8 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg\"\n              >\n                <div className=\"text-sm text-stone-600\">Premium Quality</div>\n                <div className=\"text-2xl font-bold text-accent-primary\">A+</div>\n              </motion.div>\n\n              <motion.div\n                animate={{ y: [0, 8, 0] }}\n                transition={{ duration: 4, repeat: Infinity, delay: 1 }}\n                className=\"absolute bottom-8 left-8 bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg\"\n              >\n                <div className=\"text-sm text-stone-600\">Craftsmanship</div>\n                <div className=\"text-lg font-bold text-accent-primary\">Exceptional</div>\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\"\n        >\n          <motion.div\n            animate={{ y: [0, 12, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-1 h-3 bg-white/70 rounded-full mt-2\"\n          />\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,cAAc;IAClB,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC,obAAob,CAAC;wBACzc;;;;;;kCAGF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,QAAQ;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;wBACA,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,WAAU;;sDAEV,6LAAC;4CAAK,WAAU;sDAAwG;;;;;;sDAGxH,6LAAC;4CAAG,WAAU;;gDAAuE;8DAEnF,6LAAC;oDAAK,WAAU;8DAA6B;;;;;;8DAC7C,6LAAC;oDAAK,WAAU;8DAAQ;;;;;;;;;;;;;;;;;;8CAI5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;8CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAO,WAAU;;gDAA+J;8DAE/K,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;oDAAsD,MAAM;;;;;;;;;;;;sDAEpF,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAO,MAAM;;;;;;gDAAM;;;;;;;;;;;;;8CAMvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAA0C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;;sCAM9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAG,OAAO;4BAAI;4BACtC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB,CAAC,sZAAsZ,CAAC;wCAC3a;;;;;;kDAEF,6LAAC;wCAAI,WAAU;;;;;;kDAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC;gDAAG,CAAC;gDAAI;6CAAE;wCAAC;wCAC1B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;wCAC5C,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAyB;;;;;;0DACxC,6LAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;kDAG1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC;gDAAG;gDAAG;6CAAE;wCAAC;wCACxB,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,OAAO;wCAAE;wCACtD,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;0DAAyB;;;;;;0DACxC,6LAAC;gDAAI,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAxKM;uCA0KS", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/FeaturesSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Shield, Zap, Users, Award } from 'lucide-react';\n\nconst FeaturesSection = () => {\n  const features = [\n    {\n      icon: Shield,\n      title: 'Premium Quality',\n      description: 'Uncompromising quality in every cast stone piece, built to last generations.',\n      color: 'bg-blue-500'\n    },\n    {\n      icon: Zap,\n      title: 'Fast Installation',\n      description: 'Efficient installation process that minimizes disruption to your project timeline.',\n      color: 'bg-orange-500'\n    },\n    {\n      icon: Users,\n      title: 'Expert Team',\n      description: 'Skilled craftsmen with decades of experience in architectural stone work.',\n      color: 'bg-green-500'\n    },\n    {\n      icon: Award,\n      title: 'Award Winning',\n      description: 'Recognized excellence in architectural stone design and installation.',\n      color: 'bg-purple-500'\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-accent-primary/10 text-accent-primary rounded-full text-sm font-medium mb-4\">\n            Why Choose Us\n          </span>\n          <h2 className=\"font-display text-4xl lg:text-5xl font-bold text-stone-800 mb-6\">\n            The main features\n            <span className=\"block text-accent-primary\">of our expertise</span>\n          </h2>\n          <p className=\"text-xl text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            Discover what sets Cast Stone International apart in the world of architectural stone solutions.\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              variants={itemVariants}\n              className=\"group\"\n            >\n              <div className=\"bg-stone-50 rounded-2xl p-8 h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-2\">\n                <div className={`w-16 h-16 ${feature.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  <feature.icon className=\"text-white\" size={28} />\n                </div>\n                <h3 className=\"font-display text-xl font-semibold text-stone-800 mb-4\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-stone-600 leading-relaxed\">\n                  {feature.description}\n                </p>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Magazine-style Layout Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          className=\"mt-20 grid lg:grid-cols-2 gap-12 items-center\"\n        >\n          {/* Left - Large Feature Card */}\n          <div className=\"relative\">\n            <div className=\"bg-gradient-to-br from-stone-800 to-stone-900 rounded-3xl p-8 lg:p-12 text-white\">\n              <div className=\"mb-6\">\n                <span className=\"inline-block px-3 py-1 bg-accent-tertiary/20 text-accent-tertiary rounded-full text-sm font-medium mb-4\">\n                  Innovation\n                </span>\n                <h3 className=\"font-display text-3xl font-bold mb-4\">\n                  Cutting-edge technology meets traditional craftsmanship\n                </h3>\n                <p className=\"text-stone-300 leading-relaxed\">\n                  Our state-of-the-art manufacturing process ensures precision and consistency \n                  while maintaining the artisanal quality that defines exceptional stonework.\n                </p>\n              </div>\n              <button className=\"bg-accent-primary text-white px-6 py-3 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300\">\n                Learn More\n              </button>\n            </div>\n            \n            {/* Floating Stats */}\n            <motion.div\n              animate={{ y: [0, -10, 0] }}\n              transition={{ duration: 4, repeat: Infinity }}\n              className=\"absolute -top-4 -right-4 bg-white rounded-xl p-4 shadow-lg\"\n            >\n              <div className=\"text-2xl font-bold text-accent-primary\">99.8%</div>\n              <div className=\"text-sm text-stone-600\">Precision Rate</div>\n            </motion.div>\n          </div>\n\n          {/* Right - Stacked Cards */}\n          <div className=\"space-y-6\">\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              className=\"bg-white border border-stone-200 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-accent-primary/10 rounded-lg flex items-center justify-center\">\n                  <Shield className=\"text-accent-primary\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-stone-800 mb-2\">Lifetime Warranty</h4>\n                  <p className=\"text-stone-600 text-sm\">\n                    Comprehensive warranty coverage on all our cast stone installations.\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              className=\"bg-white border border-stone-200 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-accent-primary/10 rounded-lg flex items-center justify-center\">\n                  <Users className=\"text-accent-primary\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-stone-800 mb-2\">24/7 Support</h4>\n                  <p className=\"text-stone-600 text-sm\">\n                    Round-the-clock customer support for all your project needs.\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, x: 30 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              className=\"bg-white border border-stone-200 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-12 h-12 bg-accent-primary/10 rounded-lg flex items-center justify-center\">\n                  <Award className=\"text-accent-primary\" size={24} />\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-stone-800 mb-2\">Certified Excellence</h4>\n                  <p className=\"text-stone-600 text-sm\">\n                    Industry-certified processes and materials for guaranteed quality.\n                  </p>\n                </div>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,MAAM,yMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAwG;;;;;;sCAGxH,6LAAC;4BAAG,WAAU;;gCAAkE;8CAE9E,6LAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;sCAE9C,6LAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,QAAQ,KAAK,CAAC,yGAAyG,CAAC;kDACnJ,cAAA,6LAAC,QAAQ,IAAI;4CAAC,WAAU;4CAAa,MAAM;;;;;;;;;;;kDAE7C,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;;;;;;;2BAZnB,QAAQ,KAAK;;;;;;;;;;8BAoBxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA0G;;;;;;8DAG1H,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,6LAAC;oDAAE,WAAU;8DAAiC;;;;;;;;;;;;sDAKhD,6LAAC;4CAAO,WAAU;sDAA2H;;;;;;;;;;;;8CAM/I,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG,CAAC;4CAAI;yCAAE;oCAAC;oCAC1B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,6LAAC;4CAAI,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;oDAAsB,MAAM;;;;;;;;;;;0DAEhD,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAsB,MAAM;;;;;;;;;;;0DAE/C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;8CAO5C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAsB,MAAM;;;;;;;;;;;0DAE/C,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxD;KA9MM;uCAgNS", "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/ProjectsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { ArrowRight, ExternalLink } from 'lucide-react';\n\nconst ProjectsSection = () => {\n  const projects = [\n    {\n      id: 1,\n      title: 'Modern Residential Complex',\n      category: 'Residential',\n      description: 'Luxury apartment complex featuring custom cast stone facades and architectural details.',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><rect width=\"400\" height=\"300\" fill=\"%23f5f5f4\"/><rect x=\"50\" y=\"50\" width=\"300\" height=\"200\" fill=\"%23d6d3d1\" rx=\"10\"/><rect x=\"80\" y=\"80\" width=\"80\" height=\"60\" fill=\"%23a8a29e\"/><rect x=\"180\" y=\"80\" width=\"80\" height=\"60\" fill=\"%23a8a29e\"/><rect x=\"280\" y=\"80\" width=\"80\" height=\"60\" fill=\"%23a8a29e\"/></svg>',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'Historic Cathedral Restoration',\n      category: 'Heritage',\n      description: 'Meticulous restoration of 19th-century cathedral stonework.',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><rect width=\"400\" height=\"300\" fill=\"%23e7e5e4\"/><polygon points=\"200,50 150,150 250,150\" fill=\"%23d6d3d1\"/><rect x=\"175\" y=\"150\" width=\"50\" height=\"100\" fill=\"%23a8a29e\"/><circle cx=\"200\" cy=\"120\" r=\"15\" fill=\"%238b5a3c\"/></svg>',\n      featured: false\n    },\n    {\n      id: 3,\n      title: 'Corporate Headquarters',\n      category: 'Commercial',\n      description: 'Contemporary office building with striking stone architectural elements.',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><rect width=\"400\" height=\"300\" fill=\"%23f5f5f4\"/><rect x=\"100\" y=\"80\" width=\"200\" height=\"140\" fill=\"%23d6d3d1\"/><rect x=\"120\" y=\"100\" width=\"40\" height=\"40\" fill=\"%23a8a29e\"/><rect x=\"180\" y=\"100\" width=\"40\" height=\"40\" fill=\"%23a8a29e\"/><rect x=\"240\" y=\"100\" width=\"40\" height=\"40\" fill=\"%23a8a29e\"/></svg>',\n      featured: false\n    },\n    {\n      id: 4,\n      title: 'Luxury Hotel Facade',\n      category: 'Hospitality',\n      description: 'Elegant hotel entrance featuring custom carved stone elements.',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 300\"><rect width=\"400\" height=\"300\" fill=\"%23e7e5e4\"/><rect x=\"50\" y=\"100\" width=\"300\" height=\"150\" fill=\"%23d6d3d1\" rx=\"15\"/><rect x=\"150\" y=\"150\" width=\"100\" height=\"80\" fill=\"%238b5a3c\" rx=\"5\"/><circle cx=\"200\" cy=\"130\" r=\"10\" fill=\"%236b4423\"/></svg>',\n      featured: false\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-stone-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-accent-primary/10 text-accent-primary rounded-full text-sm font-medium mb-4\">\n            Our Portfolio\n          </span>\n          <h2 className=\"font-display text-4xl lg:text-5xl font-bold text-stone-800 mb-6\">\n            Featured\n            <span className=\"block text-accent-primary\">Projects</span>\n          </h2>\n          <p className=\"text-xl text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            Explore our portfolio of exceptional cast stone projects that showcase our commitment to quality and craftsmanship.\n          </p>\n        </motion.div>\n\n        {/* Projects Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid lg:grid-cols-12 gap-8\"\n        >\n          {/* Featured Project - Large */}\n          <motion.div\n            variants={itemVariants}\n            className=\"lg:col-span-8\"\n          >\n            <div className=\"group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500\">\n              <div className=\"aspect-[16/10] overflow-hidden\">\n                <img \n                  src={projects[0].image}\n                  alt={projects[0].title}\n                  className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-500\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-stone-900/80 via-stone-900/20 to-transparent\" />\n              </div>\n              \n              <div className=\"absolute bottom-0 left-0 right-0 p-8\">\n                <span className=\"inline-block px-3 py-1 bg-accent-primary text-white rounded-full text-sm font-medium mb-3\">\n                  {projects[0].category}\n                </span>\n                <h3 className=\"font-display text-3xl font-bold text-white mb-3\">\n                  {projects[0].title}\n                </h3>\n                <p className=\"text-stone-200 mb-4 leading-relaxed\">\n                  {projects[0].description}\n                </p>\n                <button className=\"group/btn inline-flex items-center text-accent-tertiary hover:text-white transition-colors duration-300\">\n                  View Project\n                  <ArrowRight className=\"ml-2 group-hover/btn:translate-x-1 transition-transform\" size={20} />\n                </button>\n              </div>\n\n              {/* Hover Overlay */}\n              <div className=\"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                <div className=\"bg-white/90 backdrop-blur-sm rounded-full p-3\">\n                  <ExternalLink className=\"text-stone-800\" size={20} />\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Secondary Projects - Smaller */}\n          <div className=\"lg:col-span-4 space-y-8\">\n            {projects.slice(1, 3).map((project, index) => (\n              <motion.div\n                key={project.id}\n                variants={itemVariants}\n                className=\"group\"\n              >\n                <div className=\"bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\n                  <div className=\"aspect-[4/3] overflow-hidden\">\n                    <img \n                      src={project.image}\n                      alt={project.title}\n                      className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                  </div>\n                  <div className=\"p-6\">\n                    <span className=\"inline-block px-2 py-1 bg-stone-100 text-stone-600 rounded text-xs font-medium mb-2\">\n                      {project.category}\n                    </span>\n                    <h4 className=\"font-display text-xl font-semibold text-stone-800 mb-2\">\n                      {project.title}\n                    </h4>\n                    <p className=\"text-stone-600 text-sm leading-relaxed mb-4\">\n                      {project.description}\n                    </p>\n                    <button className=\"group/btn inline-flex items-center text-accent-primary hover:text-accent-secondary transition-colors duration-300\">\n                      Learn More\n                      <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform\" size={16} />\n                    </button>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Bottom Row */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          className=\"mt-12 grid md:grid-cols-2 gap-8\"\n        >\n          {projects.slice(3).map((project, index) => (\n            <div key={project.id} className=\"group\">\n              <div className=\"bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\n                <div className=\"aspect-[16/10] overflow-hidden\">\n                  <img \n                    src={project.image}\n                    alt={project.title}\n                    className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                </div>\n                <div className=\"p-6\">\n                  <span className=\"inline-block px-2 py-1 bg-stone-100 text-stone-600 rounded text-xs font-medium mb-2\">\n                    {project.category}\n                  </span>\n                  <h4 className=\"font-display text-xl font-semibold text-stone-800 mb-2\">\n                    {project.title}\n                  </h4>\n                  <p className=\"text-stone-600 text-sm leading-relaxed mb-4\">\n                    {project.description}\n                  </p>\n                  <button className=\"group/btn inline-flex items-center text-accent-primary hover:text-accent-secondary transition-colors duration-300\">\n                    View Details\n                    <ArrowRight className=\"ml-1 group-hover/btn:translate-x-1 transition-transform\" size={16} />\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))}\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.5 }}\n          className=\"text-center mt-16\"\n        >\n          <button className=\"bg-accent-primary text-white px-8 py-4 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300 inline-flex items-center\">\n            View All Projects\n            <ArrowRight className=\"ml-2\" size={20} />\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ProjectsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,aAAa;YACb,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAwG;;;;;;sCAGxH,6LAAC;4BAAG,WAAU;;gCAAkE;8CAE9E,6LAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;sCAE9C,6LAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK;gDACtB,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAK;gDACtB,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,QAAQ,CAAC,EAAE,CAAC,QAAQ;;;;;;0DAEvB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,CAAC,EAAE,CAAC,KAAK;;;;;;0DAEpB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,CAAC,EAAE,CAAC,WAAW;;;;;;0DAE1B,6LAAC;gDAAO,WAAU;;oDAA0G;kEAE1H,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;wDAA0D,MAAM;;;;;;;;;;;;;;;;;;kDAK1F,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;gDAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvD,6LAAC;4BAAI,WAAU;sCACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK,QAAQ,KAAK;oDAClB,KAAK,QAAQ,KAAK;oDAClB,WAAU;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;kEAEnB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAEtB,6LAAC;wDAAO,WAAU;;4DAAoH;0EAEpI,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;gEAA0D,MAAM;;;;;;;;;;;;;;;;;;;;;;;;mCAxBvF,QAAQ,EAAE;;;;;;;;;;;;;;;;8BAkCvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAET,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;4BAAqB,WAAU;sCAC9B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,KAAK;4CAClB,WAAU;;;;;;;;;;;kDAGd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DACb,QAAQ,QAAQ;;;;;;0DAEnB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAEhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAEtB,6LAAC;gDAAO,WAAU;;oDAAoH;kEAEpI,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;wDAA0D,MAAM;;;;;;;;;;;;;;;;;;;;;;;;2BArBpF,QAAQ,EAAE;;;;;;;;;;8BA8BxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,6LAAC;wBAAO,WAAU;;4BAAoJ;0CAEpK,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;gCAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/C;KA3NM;uCA6NS", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/components/NewsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Calendar, ArrowRight, User } from 'lucide-react';\n\nconst NewsSection = () => {\n  const newsItems = [\n    {\n      id: 1,\n      title: 'Revolutionary Stone Casting Techniques Transform Architecture',\n      excerpt: 'Our latest innovations in cast stone manufacturing are setting new industry standards for precision and sustainability.',\n      author: '<PERSON>',\n      date: '2024-06-15',\n      category: 'Innovation',\n      readTime: '5 min read',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 250\"><rect width=\"400\" height=\"250\" fill=\"%23f5f5f4\"/><rect x=\"50\" y=\"50\" width=\"300\" height=\"150\" fill=\"%23d6d3d1\" rx=\"10\"/><circle cx=\"200\" cy=\"125\" r=\"40\" fill=\"%238b5a3c\"/><rect x=\"180\" y=\"105\" width=\"40\" height=\"40\" fill=\"%236b4423\" rx=\"5\"/></svg>',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'Sustainable Practices in Modern Stone Manufacturing',\n      excerpt: 'How we\\'re reducing environmental impact while maintaining the highest quality standards.',\n      author: '<PERSON>',\n      date: '2024-06-10',\n      category: 'Sustainability',\n      readTime: '3 min read',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 250\"><rect width=\"400\" height=\"250\" fill=\"%23e7e5e4\"/><rect x=\"100\" y=\"75\" width=\"200\" height=\"100\" fill=\"%23a8a29e\" rx=\"15\"/><circle cx=\"150\" cy=\"125\" r=\"20\" fill=\"%23d4a574\"/><circle cx=\"250\" cy=\"125\" r=\"20\" fill=\"%23d4a574\"/></svg>',\n      featured: false\n    },\n    {\n      id: 3,\n      title: 'Award-Winning Heritage Restoration Project',\n      excerpt: 'Our team receives recognition for outstanding work on the historic Riverside Cathedral restoration.',\n      author: 'Emma Thompson',\n      date: '2024-06-05',\n      category: 'Awards',\n      readTime: '4 min read',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 250\"><rect width=\"400\" height=\"250\" fill=\"%23f5f5f4\"/><polygon points=\"200,75 175,125 225,125\" fill=\"%23d6d3d1\"/><rect x=\"185\" y=\"125\" width=\"30\" height=\"50\" fill=\"%23a8a29e\"/><circle cx=\"200\" cy=\"110\" r=\"8\" fill=\"%238b5a3c\"/></svg>',\n      featured: false\n    },\n    {\n      id: 4,\n      title: 'New Partnership Expands International Reach',\n      excerpt: 'Strategic alliance with European manufacturers opens new markets for our premium stone solutions.',\n      author: 'David Rodriguez',\n      date: '2024-05-28',\n      category: 'Business',\n      readTime: '6 min read',\n      image: 'data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 250\"><rect width=\"400\" height=\"250\" fill=\"%23e7e5e4\"/><rect x=\"75\" y=\"100\" width=\"250\" height=\"50\" fill=\"%23d6d3d1\" rx=\"25\"/><circle cx=\"150\" cy=\"125\" r=\"15\" fill=\"%238b5a3c\"/><circle cx=\"250\" cy=\"125\" r=\"15\" fill=\"%238b5a3c\"/></svg>',\n      featured: false\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-accent-primary/10 text-accent-primary rounded-full text-sm font-medium mb-4\">\n            Latest Updates\n          </span>\n          <h2 className=\"font-display text-4xl lg:text-5xl font-bold text-stone-800 mb-6\">\n            News and\n            <span className=\"block text-accent-primary\">Updates</span>\n          </h2>\n          <p className=\"text-xl text-stone-600 max-w-3xl mx-auto leading-relaxed\">\n            Stay informed about the latest developments, innovations, and achievements in the world of cast stone architecture.\n          </p>\n        </motion.div>\n\n        {/* Featured Article */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n          className=\"mb-16\"\n        >\n          <div className=\"bg-gradient-to-r from-stone-800 to-stone-900 rounded-3xl overflow-hidden\">\n            <div className=\"grid lg:grid-cols-2 gap-0\">\n              <div className=\"p-8 lg:p-12 text-white\">\n                <span className=\"inline-block px-3 py-1 bg-accent-primary rounded-full text-sm font-medium mb-4\">\n                  {newsItems[0].category}\n                </span>\n                <h3 className=\"font-display text-3xl lg:text-4xl font-bold mb-4 leading-tight\">\n                  {newsItems[0].title}\n                </h3>\n                <p className=\"text-stone-300 text-lg leading-relaxed mb-6\">\n                  {newsItems[0].excerpt}\n                </p>\n                \n                <div className=\"flex items-center space-x-6 mb-8\">\n                  <div className=\"flex items-center space-x-2\">\n                    <User size={16} className=\"text-stone-400\" />\n                    <span className=\"text-stone-300 text-sm\">{newsItems[0].author}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Calendar size={16} className=\"text-stone-400\" />\n                    <span className=\"text-stone-300 text-sm\">\n                      {new Date(newsItems[0].date).toLocaleDateString('en-US', { \n                        year: 'numeric', \n                        month: 'long', \n                        day: 'numeric' \n                      })}\n                    </span>\n                  </div>\n                  <span className=\"text-stone-400 text-sm\">{newsItems[0].readTime}</span>\n                </div>\n\n                <button className=\"group bg-accent-primary text-white px-6 py-3 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300 inline-flex items-center\">\n                  Read Full Article\n                  <ArrowRight className=\"ml-2 group-hover:translate-x-1 transition-transform\" size={20} />\n                </button>\n              </div>\n              \n              <div className=\"relative\">\n                <img \n                  src={newsItems[0].image}\n                  alt={newsItems[0].title}\n                  className=\"w-full h-full object-cover min-h-[400px]\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-l from-transparent to-stone-900/20\" />\n              </div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* News Grid */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {newsItems.slice(1).map((article, index) => (\n            <motion.article\n              key={article.id}\n              variants={itemVariants}\n              className=\"group\"\n            >\n              <div className=\"bg-stone-50 rounded-2xl overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1\">\n                <div className=\"aspect-[16/10] overflow-hidden\">\n                  <img \n                    src={article.image}\n                    alt={article.title}\n                    className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                </div>\n                \n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <span className=\"inline-block px-2 py-1 bg-accent-primary/10 text-accent-primary rounded text-xs font-medium\">\n                      {article.category}\n                    </span>\n                    <span className=\"text-stone-500 text-xs\">{article.readTime}</span>\n                  </div>\n                  \n                  <h4 className=\"font-display text-xl font-semibold text-stone-800 mb-3 leading-tight group-hover:text-accent-primary transition-colors duration-300\">\n                    {article.title}\n                  </h4>\n                  \n                  <p className=\"text-stone-600 text-sm leading-relaxed mb-4\">\n                    {article.excerpt}\n                  </p>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4 text-xs text-stone-500\">\n                      <div className=\"flex items-center space-x-1\">\n                        <User size={12} />\n                        <span>{article.author}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar size={12} />\n                        <span>\n                          {new Date(article.date).toLocaleDateString('en-US', { \n                            month: 'short', \n                            day: 'numeric' \n                          })}\n                        </span>\n                      </div>\n                    </div>\n                    \n                    <button className=\"group/btn text-accent-primary hover:text-accent-secondary transition-colors duration-300\">\n                      <ArrowRight className=\"group-hover/btn:translate-x-1 transition-transform\" size={16} />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </motion.article>\n          ))}\n        </motion.div>\n\n        {/* Newsletter Signup */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          className=\"mt-16 bg-stone-100 rounded-3xl p-8 lg:p-12 text-center\"\n        >\n          <h3 className=\"font-display text-3xl font-bold text-stone-800 mb-4\">\n            Stay Updated\n          </h3>\n          <p className=\"text-stone-600 mb-8 max-w-2xl mx-auto\">\n            Subscribe to our newsletter for the latest news, project updates, and industry insights delivered directly to your inbox.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n            <input \n              type=\"email\" \n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 rounded-full border border-stone-300 focus:outline-none focus:ring-2 focus:ring-accent-primary focus:border-transparent\"\n            />\n            <button className=\"bg-accent-primary text-white px-6 py-3 rounded-full font-medium hover:bg-accent-secondary transition-colors duration-300 whitespace-nowrap\">\n              Subscribe\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default NewsSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,cAAc;IAClB,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YACN,UAAU;YACV,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,MAAM;YAC<PERSON>,UAAU;YACV,UAAU;YACV,OAAO;YACP,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAwG;;;;;;sCAGxH,6LAAC;4BAAG,WAAU;;gCAAkE;8CAE9E,6LAAC;oCAAK,WAAU;8CAA4B;;;;;;;;;;;;sCAE9C,6LAAC;4BAAE,WAAU;sCAA2D;;;;;;;;;;;;8BAM1E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,SAAS,CAAC,EAAE,CAAC,QAAQ;;;;;;sDAExB,6LAAC;4CAAG,WAAU;sDACX,SAAS,CAAC,EAAE,CAAC,KAAK;;;;;;sDAErB,6LAAC;4CAAE,WAAU;sDACV,SAAS,CAAC,EAAE,CAAC,OAAO;;;;;;sDAGvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC1B,6LAAC;4DAAK,WAAU;sEAA0B,SAAS,CAAC,EAAE,CAAC,MAAM;;;;;;;;;;;;8DAE/D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC9B,6LAAC;4DAAK,WAAU;sEACb,IAAI,KAAK,SAAS,CAAC,EAAE,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS;gEACvD,MAAM;gEACN,OAAO;gEACP,KAAK;4DACP;;;;;;;;;;;;8DAGJ,6LAAC;oDAAK,WAAU;8DAA0B,SAAS,CAAC,EAAE,CAAC,QAAQ;;;;;;;;;;;;sDAGjE,6LAAC;4CAAO,WAAU;;gDAA0J;8DAE1K,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;oDAAsD,MAAM;;;;;;;;;;;;;;;;;;8CAItF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK,SAAS,CAAC,EAAE,CAAC,KAAK;4CACvB,KAAK,SAAS,CAAC,EAAE,CAAC,KAAK;4CACvB,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,UAAU,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;4BAEb,UAAU;4BACV,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,QAAQ,KAAK;4CAClB,KAAK,QAAQ,KAAK;4CAClB,WAAU;;;;;;;;;;;kDAId,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ;;;;;;kEAEnB,6LAAC;wDAAK,WAAU;kEAA0B,QAAQ,QAAQ;;;;;;;;;;;;0DAG5D,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,OAAO;;;;;;0DAGlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,MAAM;;;;;;kFACZ,6LAAC;kFAAM,QAAQ,MAAM;;;;;;;;;;;;0EAEvB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,MAAM;;;;;;kFAChB,6LAAC;kFACE,IAAI,KAAK,QAAQ,IAAI,EAAE,kBAAkB,CAAC,SAAS;4EAClD,OAAO;4EACP,KAAK;wEACP;;;;;;;;;;;;;;;;;;kEAKN,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;4DAAqD,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/CpF,QAAQ,EAAE;;;;;;;;;;8BAyDrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAIrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC;oCAAO,WAAU;8CAA6I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3K;KApPM;uCAsPS", "debugId": null}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Patricks%20Website/cast-stone-international/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Navigation from '@/components/Navigation';\nimport HeroSection from '@/components/HeroSection';\nimport FeaturesSection from '@/components/FeaturesSection';\nimport ProjectsSection from '@/components/ProjectsSection';\nimport NewsSection from '@/components/NewsSection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-stone-gradient\">\n      <Navigation />\n      <main>\n        <HeroSection />\n        <FeaturesSection />\n        <ProjectsSection />\n        <NewsSection />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAPA;;;;;;;AASe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BACX,6LAAC;;kCACC,6LAAC,oIAAA,CAAA,UAAW;;;;;kCACZ,6LAAC,wIAAA,CAAA,UAAe;;;;;kCAChB,6LAAC,wIAAA,CAAA,UAAe;;;;;kCAChB,6LAAC,oIAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIpB;KAZwB", "debugId": null}}]}