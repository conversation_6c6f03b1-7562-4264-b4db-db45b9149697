'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ChevronLeft, ChevronRight, MapPin, Calendar, User } from 'lucide-react';

const Portfolio = () => {
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const projects = [
    {
      id: 1,
      title: "Grand Cathedral Restoration",
      location: "New York, NY",
      year: "2023",
      client: "Historic Preservation Society",
      category: "Religious Architecture",
      description: "Complete restoration of 19th-century cathedral featuring intricate cast stone details, flying buttresses, and ornate facades.",
      images: [
        "https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    },
    {
      id: 2,
      title: "Luxury Resort Complex",
      location: "Miami, FL",
      year: "2023",
      client: "Oceanview Resorts",
      category: "Hospitality",
      description: "Modern resort featuring custom cast stone elements, decorative columns, and Mediterranean-inspired architectural details.",
      images: [
        "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    },
    {
      id: 3,
      title: "Corporate Headquarters",
      location: "Chicago, IL",
      year: "2022",
      client: "Fortune 500 Company",
      category: "Commercial",
      description: "Contemporary office building with sleek limestone facade, custom medallions, and modern architectural elements.",
      images: [
        "https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    },
    {
      id: 4,
      title: "Historic Mansion Renovation",
      location: "Boston, MA",
      year: "2022",
      client: "Private Residence",
      category: "Residential",
      description: "Restoration of 18th-century mansion with period-accurate cast stone details, ornate balustrades, and decorative elements.",
      images: [
        "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    },
    {
      id: 5,
      title: "University Campus Expansion",
      location: "Philadelphia, PA",
      year: "2021",
      client: "Ivy League University",
      category: "Educational",
      description: "New academic building featuring traditional collegiate Gothic architecture with modern cast stone techniques.",
      images: [
        "https://images.unsplash.com/photo-1600607688066-890987b5d394?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    },
    {
      id: 6,
      title: "Art Museum Extension",
      location: "Los Angeles, CA",
      year: "2021",
      client: "Metropolitan Art Museum",
      category: "Cultural",
      description: "Contemporary museum wing with innovative cast stone facade, sculptural elements, and artistic architectural features.",
      images: [
        "https://images.unsplash.com/photo-1600607688618-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607688888-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
        "https://images.unsplash.com/photo-1600607689372-8e2c2c9c5e5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
      ]
    }
  ];

  const openLightbox = (projectId: number) => {
    setSelectedProject(projectId);
    setCurrentImageIndex(0);
  };

  const closeLightbox = () => {
    setSelectedProject(null);
    setCurrentImageIndex(0);
  };

  const nextImage = () => {
    const project = projects.find(p => p.id === selectedProject);
    if (project) {
      setCurrentImageIndex((prev) => 
        prev === project.images.length - 1 ? 0 : prev + 1
      );
    }
  };

  const prevImage = () => {
    const project = projects.find(p => p.id === selectedProject);
    if (project) {
      setCurrentImageIndex((prev) => 
        prev === 0 ? project.images.length - 1 : prev - 1
      );
    }
  };

  const selectedProjectData = projects.find(p => p.id === selectedProject);

  return (
    <section id="portfolio" className="py-24 bg-stone-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-block mb-4">
            <span className="text-purple-600 font-semibold text-lg tracking-wide uppercase">
              Our Portfolio
            </span>
            <div className="w-20 h-1 bg-purple-600 mt-2 mx-auto"></div>
          </div>
          
          <h2 className="font-display text-4xl md:text-5xl font-bold text-stone-800 mb-6">
            Architectural
            <span className="text-gradient block">Masterpieces</span>
          </h2>
          
          <p className="text-lg text-stone-600 max-w-3xl mx-auto leading-relaxed">
            Explore our portfolio of exceptional projects that showcase our expertise 
            in creating timeless architectural elements.
          </p>
        </motion.div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group cursor-pointer"
              onClick={() => openLightbox(project.id)}
            >
              <div className="bg-white rounded-xl overflow-hidden luxury-shadow hover:shadow-2xl transition-all duration-500">
                {/* Image */}
                <div className="relative overflow-hidden h-64">
                  <img
                    src={project.images[0]}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {project.category}
                    </span>
                  </div>
                  
                  {/* Overlay Info */}
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <h3 className="font-semibold text-xl mb-1">{project.title}</h3>
                    <div className="flex items-center space-x-4 text-sm opacity-90">
                      <span className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4" />
                        <span>{project.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{project.year}</span>
                      </span>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <p className="text-stone-600 leading-relaxed mb-4">
                    {project.description.substring(0, 120)}...
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-stone-500 text-sm">
                      <User className="w-4 h-4" />
                      <span>{project.client}</span>
                    </div>
                    <span className="text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300">
                      View Project
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Lightbox */}
      <AnimatePresence>
        {selectedProject && selectedProjectData && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
            onClick={closeLightbox}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-6 border-b border-stone-200 flex items-center justify-between">
                <div>
                  <h3 className="font-display text-2xl font-bold text-stone-800">
                    {selectedProjectData.title}
                  </h3>
                  <p className="text-stone-600">{selectedProjectData.location} • {selectedProjectData.year}</p>
                </div>
                <button
                  onClick={closeLightbox}
                  className="p-2 hover:bg-stone-100 rounded-full transition-colors duration-200"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* Image Gallery */}
              <div className="relative">
                <img
                  src={selectedProjectData.images[currentImageIndex]}
                  alt={selectedProjectData.title}
                  className="w-full h-96 object-cover"
                />
                
                {/* Navigation Arrows */}
                {selectedProjectData.images.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white hover:bg-white/30 transition-colors duration-300"
                    >
                      <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white hover:bg-white/30 transition-colors duration-300"
                    >
                      <ChevronRight className="w-6 h-6" />
                    </button>
                  </>
                )}

                {/* Image Indicators */}
                {selectedProjectData.images.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {selectedProjectData.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-colors duration-200 ${
                          index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                        }`}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Project Details */}
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-lg text-stone-800 mb-2">Project Description</h4>
                    <p className="text-stone-600 leading-relaxed">{selectedProjectData.description}</p>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h5 className="font-medium text-stone-800">Client</h5>
                      <p className="text-stone-600">{selectedProjectData.client}</p>
                    </div>
                    <div>
                      <h5 className="font-medium text-stone-800">Category</h5>
                      <p className="text-stone-600">{selectedProjectData.category}</p>
                    </div>
                    <div>
                      <h5 className="font-medium text-stone-800">Completion Year</h5>
                      <p className="text-stone-600">{selectedProjectData.year}</p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default Portfolio;
