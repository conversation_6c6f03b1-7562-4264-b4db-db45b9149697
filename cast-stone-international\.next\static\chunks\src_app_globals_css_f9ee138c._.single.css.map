{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');\n@layer properties;\n.row-start-2 {\n  grid-row-start: 2;\n}\n.row-start-3 {\n  grid-row-start: 3;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-full {\n  width: 100%;\n}\n.list-inside {\n  list-style-position: inside;\n}\n.list-decimal {\n  list-style-type: decimal;\n}\n.grid-rows-\\[20px_1fr_20px\\] {\n  grid-template-rows: 20px 1fr 20px;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-center {\n  align-items: center;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-items-center {\n  justify-items: center;\n}\n.gap-\\[24px\\] {\n  gap: 24px;\n}\n.gap-\\[32px\\] {\n  gap: 32px;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-solid {\n  --tw-border-style: solid;\n  border-style: solid;\n}\n.border-transparent {\n  border-color: transparent;\n}\n.text-center {\n  text-align: center;\n}\n.font-\\[family-name\\:var\\(--font-geist-mono\\)\\] {\n  font-family: var(--font-geist-mono);\n}\n.font-\\[family-name\\:var\\(--font-geist-sans\\)\\] {\n  font-family: var(--font-geist-sans);\n}\n.tracking-\\[-\\.01em\\] {\n  --tw-tracking: -.01em;\n  letter-spacing: -.01em;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.hover\\:border-transparent {\n  &:hover {\n    @media (hover: hover) {\n      border-color: transparent;\n    }\n  }\n}\n.hover\\:bg-\\[\\#383838\\] {\n  &:hover {\n    @media (hover: hover) {\n      background-color: #383838;\n    }\n  }\n}\n.hover\\:bg-\\[\\#f2f2f2\\] {\n  &:hover {\n    @media (hover: hover) {\n      background-color: #f2f2f2;\n    }\n  }\n}\n.hover\\:underline {\n  &:hover {\n    @media (hover: hover) {\n      text-decoration-line: underline;\n    }\n  }\n}\n.hover\\:underline-offset-4 {\n  &:hover {\n    @media (hover: hover) {\n      text-underline-offset: 4px;\n    }\n  }\n}\n.dark\\:invert {\n  @media (prefers-color-scheme: dark) {\n    --tw-invert: invert(100%);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n}\n.dark\\:hover\\:bg-\\[\\#1a1a1a\\] {\n  @media (prefers-color-scheme: dark) {\n    &:hover {\n      @media (hover: hover) {\n        background-color: #1a1a1a;\n      }\n    }\n  }\n}\n.dark\\:hover\\:bg-\\[\\#ccc\\] {\n  @media (prefers-color-scheme: dark) {\n    &:hover {\n      @media (hover: hover) {\n        background-color: #ccc;\n      }\n    }\n  }\n}\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\nhtml {\n  scroll-behavior: smooth;\n}\nbody {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  background: #ffffff;\n  color: #1f2937;\n  line-height: 1.6;\n  overflow-x: hidden;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.font-display {\n  font-family: 'Playfair Display', serif;\n}\n.font-body {\n  font-family: 'Inter', sans-serif;\n}\n::-webkit-scrollbar {\n  width: 6px;\n}\n::-webkit-scrollbar-track {\n  background: #f8fafc;\n}\n::-webkit-scrollbar-thumb {\n  background: #8b5cf6;\n  border-radius: 3px;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: #7c3aed;\n}\n.gradient-purple {\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);\n}\n.gradient-purple-light {\n  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\n}\n.glass {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n.float {\n  animation: float 6s ease-in-out infinite;\n}\n@keyframes pulse-glow {\n  0%, 100% {\n    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);\n  }\n}\n.pulse-glow {\n  animation: pulse-glow 2s ease-in-out infinite;\n}\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n.shimmer {\n  position: relative;\n  overflow: hidden;\n}\n.shimmer::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transform: translateX(-100%);\n  animation: shimmer 2s infinite;\n}\n.transition-all {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n.btn-primary {\n  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n  color: white;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  border: none;\n  cursor: pointer;\n}\n.btn-primary:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.3);\n}\n.btn-secondary {\n  background: transparent;\n  color: #8b5cf6;\n  padding: 12px 24px;\n  border-radius: 12px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  border: 2px solid #8b5cf6;\n  cursor: pointer;\n}\n.btn-secondary:hover {\n  background: #8b5cf6;\n  color: white;\n  transform: translateY(-2px);\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-border-style: solid;\n      --tw-tracking: initial;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n    }\n  }\n}\r\n"], "names": [], "mappings": "AAEA;EA2UE;IACE;;;;;;;;;;;;;;;;;;;;AA3UJ;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;;AAOI;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAOvB;EAAuB;;;;;AAMzB;EAAqC;;;;;;AAMrC;EAEI;IAAuB;;;;;;AAO3B;EAEI;IAAuB;;;;;;AAM7B;;;;;;AAKA;;;;AAGA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;;AAQA;;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAGA;;;;;;;;;;;AAUA;;;;;AAIA;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA"}}]}