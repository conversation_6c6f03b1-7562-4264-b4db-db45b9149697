import type { Metada<PERSON> } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";
import PerformanceMonitor from "@/components/PerformanceMonitor";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair-display",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Cast Stone International - Architectural Excellence",
  description: "Premium cast stone and natural stone architectural elements. Transforming visions into timeless masterpieces for over 25 years.",
  keywords: "cast stone, natural stone, architectural elements, stone craftsmanship, building materials, restoration, custom design",
  authors: [{ name: "Cast Stone International" }],
  creator: "Cast Stone International",
  publisher: "Cast Stone International",
  openGraph: {
    title: "Cast Stone International - Architectural Excellence",
    description: "Premium cast stone and natural stone architectural elements. Transforming visions into timeless masterpieces.",
    url: "https://caststoneinternational.com",
    siteName: "Cast Stone International",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Cast Stone International - Architectural Excellence",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Cast Stone International - Architectural Excellence",
    description: "Premium cast stone and natural stone architectural elements.",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="theme-color" content="#9333ea" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body
        className={`${inter.variable} ${playfairDisplay.variable} antialiased`}
      >
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  );
}
